import { Queue } from 'bull';

import { PROCESSOR_NAMES, QUEUE_NAMES } from '@/shared/constants/queue.constants';
import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue(QUEUE_NAMES.JOB_SCOUTING) private readonly jobScoutingQueue: Queue,
    @InjectQueue(QUEUE_NAMES.ATS_CANDIDATE_FETCH) private readonly atsCandidateQueue: Queue,
    @InjectQueue(QUEUE_NAMES.MATCH_RANK) private readonly matchRankQueue: Queue,
    @InjectQueue(QUEUE_NAMES.CAREER_INSIGHTS) private readonly careerInsightsQueue: Queue,
    @InjectQueue(QUEUE_NAMES.CANDIDATE_COMPARISON) private readonly comparisonQueue: Queue,
  ) {}

  async addJobScoutingTask(jobId: string, searchCriteria: any) {
    return await this.jobScoutingQueue.add(
      PROCESSOR_NAMES.SCOUT_CANDIDATES,
      {
        jobId,
        searchCriteria,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
      },
    );
  }

  async addAtsCandidateFetchTask(
    jobId: string,
    clientId: string,
    atsProvider: string,
    initialBatchSize: number = 100,
  ) {
    return await this.atsCandidateQueue.add(
      PROCESSOR_NAMES.FETCH_CANDIDATES,
      {
        jobId,
        clientId,
        atsProvider,
        initialBatchSize,
        isInitialFetch: true,
        pageCount: 0,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
      },
    );
  }

  async getJobStatus(jobId: string) {
    const jobs = await this.jobScoutingQueue.getJobs(['completed', 'failed', 'active', 'waiting']);
    return jobs.find((job) => job.data.jobId === jobId);
  }

  async getAtsCandidateFetchStatus(jobId: string): Promise<any> {
    try {
      // Get the queue
      const queue = this.atsCandidateQueue;

      // Get the job by its ID
      const job = await queue.getJob(jobId);

      if (!job) {
        this.logger.warn(`Job with ID ${jobId} not found in ats-candidate-fetch queue`);
        return null;
      }

      // Log the job status
      this.logger.log(`Job ${jobId} status: ${await job.getState()}, progress: ${job.progress()}`);

      // Get job data including result
      const jobData = {
        id: job.id,
        data: job.data,
        progress: job.progress(),
        returnvalue: job.returnvalue,
        finishedOn: job.finishedOn,
        processedOn: job.processedOn,
        failedReason: job.failedReason,
        stacktrace: job.stacktrace,
        opts: job.opts,
      };

      // Make sure we're returning the candidate data in the result
      if (jobData.returnvalue && !jobData.returnvalue.candidates && jobData.returnvalue.data) {
        jobData.returnvalue.candidates = jobData.returnvalue.data;
      }

      return jobData;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error getting ATS candidate fetch status: ${errorMessage}`, errorStack);
      throw error;
    }
  }

  async getAllAtsCandidateFetchJobs() {
    return await this.atsCandidateQueue.getJobs(['completed', 'failed', 'active', 'waiting']);
  }

  async addMatchRankTask(
    jobId: string,
    clientId: string,
    options: { topTierThreshold?: number; secondTierThreshold?: number } = {},
  ) {
    return await this.matchRankQueue.add(
      PROCESSOR_NAMES.PROCESS_MATCH_RANK,
      {
        jobId,
        clientId,
        topTierThreshold: options.topTierThreshold,
        secondTierThreshold: options.secondTierThreshold,
      },
      {
        attempts: 5, // Increased from 3 to 5 for better reliability
        backoff: {
          type: 'exponential',
          delay: 2000, // Increased from 1000 to 2000 for better spacing between retries
        },
        removeOnComplete: false, // Keep completed jobs for debugging
        removeOnFail: false, // Keep failed jobs for debugging
      },
    );
  }

  async getMatchRankStatus(jobId: string) {
    try {
      // Check if the jobId is a queue job ID (numeric) or a job entity ID (UUID)
      const isQueueJobId = /^\d+$/.test(jobId);

      if (isQueueJobId) {
        // If it's a queue job ID, get the job directly by its ID
        this.logger.log(`Getting status for queue job ID: ${jobId}`);

        try {
          // Get the job directly from the queue by its ID
          const job = await this.matchRankQueue.getJob(jobId);

          if (!job) {
            return {
              jobId,
              status: 'not_found',
              progress: 0,
              message: 'Queue job not found',
            };
          }

          // Get the job state
          const state = await job.getState();

          // Map Bull state to our status format
          let status = 'queued';
          switch (state) {
            case 'active':
              status = 'active';
              break;
            case 'completed':
              status = 'completed';
              break;
            case 'failed':
              status = 'failed';
              break;
            case 'delayed':
            case 'waiting':
              status = 'queued';
              break;
          }

          // Get progress
          const progress = job.progress() || 0;

          // Get result if completed
          const result = job.returnvalue;

          // Extract the actual job entity ID from the job data or result
          // Priority: result.jobId -> data.jobId -> worker job ID (fallback)
          const actualJobId = result?.jobId || job.data?.jobId || jobId;

          // Prepare response
          const response = {
            id: job.id.toString(), // Keep worker job ID for reference
            jobId: actualJobId, // Use actual job entity ID
            status,
            progress: status === 'completed' ? 100 : progress, // Ensure completed jobs show 100% progress
            message:
              status === 'active'
                ? `Processing match and rank (${progress}% complete)`
                : status === 'queued'
                  ? 'Waiting to start match and rank process'
                  : status === 'completed'
                    ? 'Match and rank process completed successfully'
                    : `Match and rank job is ${status}`,
            result,
          };

          // Add error information if failed
          if (status === 'failed' && job.failedReason) {
            response.message = job.failedReason;
          }

          // Log the response for debugging
          this.logger.log(`Match rank status for queue job ${jobId}: ${JSON.stringify(response)}`);

          return response;
        } catch (error: any) {
          this.logger.error(`Error getting match rank status for queue job ${jobId}:`, error);
          return {
            jobId,
            status: 'error',
            progress: 0,
            message: `Error getting job status: ${error.message}`,
          };
        }
      } else {
        // If it's a job entity ID (UUID), find the job in the queue by its data.jobId
        // Get all jobs from the queue
        const jobs = await this.matchRankQueue.getJobs([
          'completed',
          'failed',
          'active',
          'waiting',
        ]);

        // Find the job with the matching jobId in the data
        const job = jobs.find((job) => job.data.jobId === jobId);

        if (!job) {
          this.logger.warn(`Job with jobId ${jobId} not found in match-rank queue`);
          return {
            jobId,
            status: 'not_found',
            progress: 0,
            message: 'Job not found in queue',
          };
        }

        // Get the job state
        const state = await job.getState();

        // Map Bull state to our status format
        let status = 'queued';
        switch (state) {
          case 'active':
            status = 'active';
            break;
          case 'completed':
            status = 'completed';
            break;
          case 'failed':
            status = 'failed';
            break;
          case 'delayed':
          case 'waiting':
            status = 'queued';
            break;
        }

        // Get progress
        const progress = job.progress() || 0;

        // Get result if completed
        const result = job.returnvalue;

        // Extract the actual job entity ID from the job data or result
        // Priority: result.jobId -> data.jobId -> worker job ID (fallback)
        const actualJobId = result?.jobId || job.data?.jobId || jobId;

        // Prepare response
        const response = {
          id: job.id.toString(), // Keep worker job ID for reference
          jobId: actualJobId, // Use actual job entity ID
          status,
          progress: status === 'completed' ? 100 : progress, // Ensure completed jobs show 100% progress
          message:
            status === 'active'
              ? `Processing match and rank (${progress}% complete)`
              : status === 'queued'
                ? 'Waiting to start match and rank process'
                : status === 'completed'
                  ? 'Match and rank process completed successfully'
                  : `Match and rank job is ${status}`,
          result,
        };

        // Add error information if failed
        if (status === 'failed' && job.failedReason) {
          response.message = job.failedReason;
        }

        return response;
      }
    } catch (error: any) {
      this.logger.error(`Error getting match rank status for job ${jobId}:`, error);
      return {
        jobId,
        status: 'error',
        progress: 0,
        message: `Error getting job status: ${error.message}`,
      };
    }
  }

  async cancelMatchRankJob(jobId: string) {
    try {
      // Check if the jobId is a queue job ID (numeric) or a job entity ID (UUID)
      const isQueueJobId = /^\d+$/.test(jobId);

      if (isQueueJobId) {
        // If it's a queue job ID, get the job directly by its ID
        this.logger.log(`Cancelling queue job with ID: ${jobId}`);

        try {
          // Get the job directly from the queue by its ID
          const job = await this.matchRankQueue.getJob(jobId);

          if (!job) {
            return {
              success: false,
              message: `Queue job with ID ${jobId} not found`,
            };
          }

          // Remove the job from the queue
          await job.remove();

          return {
            success: true,
            message: `Match rank job ${jobId} cancelled successfully`,
          };
        } catch (error: any) {
          this.logger.error(`Error cancelling match rank queue job ${jobId}:`, error);
          return {
            success: false,
            message: `Error cancelling job: ${error.message}`,
          };
        }
      } else {
        // If it's a job entity ID (UUID), find the job in the queue by its data.jobId
        // Get all jobs from the queue
        const jobs = await this.matchRankQueue.getJobs(['active', 'waiting', 'delayed']);

        // Find the job with the matching jobId in the data
        const job = jobs.find((job) => job.data.jobId === jobId);

        if (!job) {
          this.logger.warn(
            `Job with jobId ${jobId} not found in match-rank queue or already completed/failed`,
          );
          return {
            success: false,
            message: 'Job not found or already completed/failed',
          };
        }

        // Remove the job from the queue
        await job.remove();

        return {
          success: true,
          message: `Match rank job ${jobId} cancelled successfully`,
        };
      }
    } catch (error: any) {
      this.logger.error(`Error cancelling match rank job ${jobId}:`, error);
      return {
        success: false,
        message: `Error cancelling job: ${error.message}`,
      };
    }
  }

  async addCareerInsightTask(insightId: string, userId: string, insightType: string, params: any) {
    return await this.careerInsightsQueue.add(
      PROCESSOR_NAMES.PROCESS_CAREER_INSIGHT,
      {
        insightId,
        userId,
        insightType,
        params,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: false,
        removeOnFail: false,
      },
    );
  }

  async getCareerInsightStatus(jobId: string) {
    try {
      const job = await this.careerInsightsQueue.getJob(jobId);

      if (!job) {
        return {
          jobId,
          status: 'not_found',
          progress: 0,
          message: 'Job not found',
        };
      }

      const state = await job.getState();
      const progress = job.progress() || 0;

      let status = 'queued';
      switch (state) {
        case 'active':
          status = 'processing';
          break;
        case 'completed':
          status = 'completed';
          break;
        case 'failed':
          status = 'failed';
          break;
        case 'delayed':
        case 'waiting':
          status = 'queued';
          break;
      }

      return {
        jobId,
        status,
        progress,
        message:
          status === 'processing'
            ? `Generating career insight (${progress}% complete)`
            : status === 'completed'
              ? 'Career insight generated successfully'
              : status === 'failed'
                ? job.failedReason || 'Failed to generate insight'
                : 'Waiting to process',
        result: job.returnvalue,
      };
    } catch (error: any) {
      this.logger.error(`Error getting career insight status for job ${jobId}:`, error);
      return {
        jobId,
        status: 'error',
        progress: 0,
        message: `Error getting job status: ${error.message}`,
      };
    }
  }

  async addComparisonTask(comparisonId: string, userId: string) {
    return await this.comparisonQueue.add(
      PROCESSOR_NAMES.PROCESS_COMPARISON,
      {
        comparisonId,
        userId,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: false,
        removeOnFail: false,
      },
    );
  }

  async getComparisonStatus(jobId: string) {
    try {
      const job = await this.comparisonQueue.getJob(jobId);

      if (!job) {
        return {
          jobId,
          status: 'not_found',
          progress: 0,
          message: 'Job not found',
        };
      }

      const state = await job.getState();
      const progress = job.progress() || 0;

      let status = 'queued';
      switch (state) {
        case 'active':
          status = 'processing';
          break;
        case 'completed':
          status = 'completed';
          break;
        case 'failed':
          status = 'failed';
          break;
        case 'delayed':
        case 'waiting':
          status = 'queued';
          break;
      }

      return {
        jobId,
        status,
        progress,
        message:
          status === 'processing'
            ? `Comparing candidates (${progress}% complete)`
            : status === 'completed'
              ? 'Comparison completed successfully'
              : status === 'failed'
                ? job.failedReason || 'Failed to complete comparison'
                : 'Waiting to process',
        result: job.returnvalue,
      };
    } catch (error: any) {
      this.logger.error(`Error getting comparison status for job ${jobId}:`, error);
      return {
        jobId,
        status: 'error',
        progress: 0,
        message: `Error getting job status: ${error.message}`,
      };
    }
  }
}
