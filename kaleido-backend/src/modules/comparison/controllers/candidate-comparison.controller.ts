import { Auth0Guard } from '@/auth/auth.guard';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { QueueService } from '@/modules/queue/queue.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreateComparisonDto, ScenarioComparisonDto } from '../dto/create-comparison.dto';
import { CandidateComparison } from '../entities/candidate-comparison.entity';
import { CandidateComparisonService } from '../services/candidate-comparison.service';

@ApiTags('Candidate Comparisons')
@ApiBearerAuth()
@UseGuards(Auth0Guard)
@Controller('comparisons')
export class CandidateComparisonController {
  constructor(
    private readonly comparisonService: CandidateComparisonService,
    private readonly queueService: QueueService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new candidate comparison' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Comparison created successfully',
    type: CandidateComparison,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job or candidates not found',
  })
  async createComparison(
    @Body() createComparisonDto: CreateComparisonDto,
    @GetUser() user: User,
  ): Promise<{
    id: string;
    queueJobId: string;
    status: string;
    message: string;
  }> {
    const comparison = await this.comparisonService.createComparison(
      user.userId,
      createComparisonDto,
    );

    return {
      id: comparison.id,
      queueJobId: (comparison as any).queueJobId,
      status: comparison.status,
      message: 'Comparison initiated. Check status for results.',
    };
  }

  @Post('scenario')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a scenario-based candidate comparison' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Scenario comparison created successfully',
    type: CandidateComparison,
  })
  async createScenarioComparison(
    @Body() scenarioDto: ScenarioComparisonDto,
    @GetUser() user: User,
  ): Promise<{
    id: string;
    queueJobId: string;
    status: string;
    message: string;
  }> {
    const comparison = await this.comparisonService.createScenarioComparison(
      user.userId,
      scenarioDto,
    );

    return {
      id: comparison.id,
      queueJobId: (comparison as any).queueJobId,
      status: comparison.status,
      message: 'Scenario comparison initiated. Check status for results.',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get comparison results' })
  @ApiParam({ name: 'id', description: 'Comparison ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Comparison details',
    type: CandidateComparison,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Comparison not found',
  })
  async getComparison(
    @Param('id') id: string,
    @GetUser() user: User,
  ): Promise<CandidateComparison> {
    return this.comparisonService.getComparison(id, user.userId);
  }

  @Get(':id/status')
  @ApiOperation({ summary: 'Get comparison processing status' })
  @ApiParam({ name: 'id', description: 'Comparison ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Comparison status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['pending', 'completed', 'failed'] },
        progress: { type: 'number', minimum: 0, maximum: 100 },
      },
    },
  })
  async getComparisonStatus(
    @Param('id') id: string,
    @GetUser() user: User,
  ): Promise<{ status: string; progress?: number }> {
    return this.comparisonService.getComparisonStatus(id, user.userId);
  }

  @Get('queue/:jobId/status')
  @ApiOperation({ summary: 'Get queue job status for comparison' })
  @ApiParam({ name: 'jobId', description: 'Queue Job ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue job status',
    schema: {
      type: 'object',
      properties: {
        jobId: { type: 'string' },
        status: { type: 'string', enum: ['queued', 'processing', 'completed', 'failed'] },
        progress: { type: 'number', minimum: 0, maximum: 100 },
        message: { type: 'string' },
        result: { type: 'object' },
      },
    },
  })
  async getQueueStatus(@Param('jobId') jobId: string): Promise<any> {
    return this.queueService.getComparisonStatus(jobId);
  }

  @Get('jobs/:jobId')
  @ApiOperation({ summary: 'List all comparisons for a job' })
  @ApiParam({ name: 'jobId', description: 'Job ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of comparisons',
    type: [CandidateComparison],
  })
  async getJobComparisons(
    @Param('jobId') jobId: string,
    @GetUser() user: User,
  ): Promise<CandidateComparison[]> {
    return this.comparisonService.getJobComparisons(jobId, user.userId);
  }

  @Get(':id/report')
  @ApiOperation({ summary: 'Generate comparison report' })
  @ApiParam({ name: 'id', description: 'Comparison ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Comparison report data',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Comparison not yet completed',
  })
  async generateReport(@Param('id') id: string, @GetUser() user: User): Promise<any> {
    return this.comparisonService.generateComparisonReport(id, user.userId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a comparison' })
  @ApiParam({ name: 'id', description: 'Comparison ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Comparison deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Comparison not found',
  })
  async deleteComparison(@Param('id') id: string, @GetUser() user: User): Promise<void> {
    await this.comparisonService.deleteComparison(id, user.userId);
  }

  @Get('config/options')
  @ApiOperation({ summary: 'Get available comparison options and configurations' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Comparison configuration options',
  })
  getComparisonOptions(): any {
    // Import the config at runtime to avoid circular dependencies
    const { COMPARISON_OPTIONS } = require('../config/comparison-options.config');
    return COMPARISON_OPTIONS;
  }
}
