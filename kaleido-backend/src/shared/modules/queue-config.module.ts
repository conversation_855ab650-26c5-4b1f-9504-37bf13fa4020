import { Lo<PERSON>, Module, forwardRef } from '@nestjs/common';
import { DEFAULT_JOB_OPTIONS, QUEUE_NAMES } from '../constants/queue.constants';

import { SubscriptionModule } from '@/modules/subscription/subscription.module';
import { BullModule } from '@nestjs/bull';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AtsModule } from '../../modules/ats/ats.module';
import { CandidateModule } from '../../modules/candidate/candidate.module';
import { Candidate } from '../../modules/candidate/entities/candidate.entity';
import { Job } from '../../modules/job/entities/job.entity';
import { JobModule } from '../../modules/job/job.module';
import { FileUploadProcessor } from '../processors/file-upload.processor';
import { ScoutProcessor } from '../processors/scout.processor';
import { ApolloLocationMappingService } from '../services/apollo-location-mapping.service';
import { ApolloScoutService } from '../services/apollo-scout.service';
import { ApolloModule } from '../services/apollo/apollo.module';
import { LinkedInScoutService } from '../services/linkedin-scout.service';
import { PDLScoutService } from '../services/pdl-scout.service';
import { RedisConfigService } from '../services/redis-config.service';
import { ResumePerformanceMonitorService } from '../services/resume-performance-monitor.service';
import { RedisModule } from './redis.module';

/**
 * Centralized module for Bull queue configuration
 * This module registers all queues used throughout the application
 * and provides a consistent configuration for all queues
 */
@Module({
  imports: [
    // Import required modules
    RedisModule,
    ApolloModule,
    // Re-enable CandidateModule with forwardRef to properly handle circular dependency
    forwardRef(() => CandidateModule),
    forwardRef(() => JobModule),
    // Make sure AtsModule is properly imported with forwardRef
    forwardRef(() => AtsModule),
    forwardRef(() => SubscriptionModule),
    TypeOrmModule.forFeature([Candidate, Job]),

    // Configure Bull with Redis
    BullModule.forRootAsync({
      imports: [ConfigModule, RedisModule],
      useFactory: async (redisConfigService: RedisConfigService) => {
        // Get the Redis configuration from the centralized service
        const config = redisConfigService.getBullRedisConfig();

        // Log the configuration being used
        const logger = new Logger('BullModule');
        logger.log('Initializing Bull with Redis configuration');

        return config;
      },
      inject: [RedisConfigService],
    }),

    // Register all queues with their default options
    BullModule.registerQueue({
      name: QUEUE_NAMES.FILE_UPLOAD,
      defaultJobOptions: DEFAULT_JOB_OPTIONS.FILE_UPLOAD,
    }),

    BullModule.registerQueue({
      name: QUEUE_NAMES.SCOUT,
      defaultJobOptions: DEFAULT_JOB_OPTIONS.SCOUT,
    }),

    BullModule.registerQueue({
      name: QUEUE_NAMES.JOB_SCOUTING,
      defaultJobOptions: DEFAULT_JOB_OPTIONS.STANDARD,
    }),

    BullModule.registerQueue({
      name: QUEUE_NAMES.ATS_CANDIDATE_FETCH,
      defaultJobOptions: DEFAULT_JOB_OPTIONS.STANDARD,
    }),

    BullModule.registerQueue({
      name: QUEUE_NAMES.MATCH_RANK,
      defaultJobOptions: DEFAULT_JOB_OPTIONS.MATCH_RANK,
    }),

    BullModule.registerQueue({
      name: QUEUE_NAMES.VIDEO_JD,
      defaultJobOptions: DEFAULT_JOB_OPTIONS.VIDEO_JD,
    }),

    BullModule.registerQueue({
      name: QUEUE_NAMES.CAREER_INSIGHTS,
      defaultJobOptions: DEFAULT_JOB_OPTIONS.STANDARD,
    }),

    BullModule.registerQueue({
      name: QUEUE_NAMES.CANDIDATE_COMPARISON,
      defaultJobOptions: DEFAULT_JOB_OPTIONS.STANDARD,
    }),

    // Register job-scraping queue with async configuration
    BullModule.registerQueueAsync({
      name: QUEUE_NAMES.JOB_SCRAPING,
      imports: [ConfigModule, RedisModule],
      useFactory: async (redisConfigService: RedisConfigService) => {
        const config = redisConfigService.getBullRedisConfig();
        return {
          ...config,
          defaultJobOptions: DEFAULT_JOB_OPTIONS.JOB_SCRAPING,
        };
      },
      inject: [RedisConfigService],
    }),
  ],
  providers: [
    // Register processors
    FileUploadProcessor,
    ScoutProcessor,
    LinkedInScoutService,
    ApolloScoutService,
    ApolloLocationMappingService, // Add the missing dependency
    PDLScoutService,
    ResumePerformanceMonitorService, // Add ResumePerformanceMonitorService
  ],
  exports: [
    // Export BullModule for use in other modules
    BullModule,
    // Export scout services for use in controllers
    LinkedInScoutService,
    ApolloScoutService,
    ApolloLocationMappingService, // Export for use in other modules
    PDLScoutService,
    ResumePerformanceMonitorService, // Export ResumePerformanceMonitorService
  ],
})
export class QueueConfigModule {}
