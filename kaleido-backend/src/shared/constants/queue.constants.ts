/**
 * Centralized constants for Bull queue names
 * This file contains all queue names used throughout the application
 * to ensure consistency and avoid typos
 */

export const QUEUE_NAMES = {
  // File upload related queues
  FILE_UPLOAD: 'file-upload',

  // Job related queues
  JOB_SCOUTING: 'job-scouting',
  JOB_SCRAPING: 'job-scraping',

  // Candidate related queues
  ATS_CANDIDATE_FETCH: 'ats-candidate-fetch',

  // Matching related queues
  MATCH_RANK: 'match-rank',

  // Scouting related queues
  SCOUT: 'scout',

  // Video related queues
  VIDEO_JD: 'video-jd',

  // Career insights related queues
  CAREER_INSIGHTS: 'career-insights',

  // Candidate comparison related queues
  CANDIDATE_COMPARISON: 'candidate-comparison',
} as const;

/**
 * Type for queue names to ensure type safety when using queue names
 */
export type QueueName = (typeof QUEUE_NAMES)[keyof typeof QUEUE_NAMES];

/**
 * Default job options for different queue types
 */
export const DEFAULT_JOB_OPTIONS = {
  // Standard job options (3 retries, exponential backoff)
  STANDARD: {
    attempts: 3,
    backoff: {
      type: 'exponential' as const,
      delay: 1000,
    },
    removeOnComplete: false,
    removeOnFail: false,
  },

  // File upload job options (5 retries, exponential backoff)
  FILE_UPLOAD: {
    attempts: 5,
    backoff: {
      type: 'exponential' as const,
      delay: 1000,
    },
    removeOnComplete: true,
    removeOnFail: false,
  },

  // Scout job options (3 retries, longer delay)
  SCOUT: {
    attempts: 3,
    backoff: {
      type: 'exponential' as const,
      delay: 2000,
    },
    removeOnComplete: true,
    removeOnFail: false,
  },

  // Job scraping options (3 retries, with timeout)
  JOB_SCRAPING: {
    attempts: 3,
    backoff: {
      type: 'exponential' as const,
      delay: 1000,
    },
    removeOnComplete: true,
    timeout: 300000, // 5 minutes
  },

  // Video JD monitoring options (long-running job with fewer retries)
  VIDEO_JD: {
    attempts: 2,
    backoff: {
      type: 'exponential' as const,
      delay: 30000, // 30 seconds between retries
    },
    removeOnComplete: false,
    removeOnFail: false,
    timeout: 3600000, // 1 hour timeout
  },

  // Match rank processing options (with timeout to prevent hanging)
  MATCH_RANK: {
    attempts: 3,
    backoff: {
      type: 'exponential' as const,
      delay: 5000, // 5 seconds between retries
    },
    removeOnComplete: false,
    removeOnFail: false,
    timeout: 900000, // 15 minutes timeout
  },
} as const;

/**
 * Queue processor names
 * These are the names of the processors used in @Process decorators
 */
export const PROCESSOR_NAMES = {
  // File upload processors
  PROCESS_UPLOADS: 'process-uploads',

  // Job scouting processors
  SCOUT_CANDIDATES: 'scout-candidates',

  // ATS processors
  FETCH_CANDIDATES: 'fetch-candidates',

  // Match rank processors
  PROCESS_MATCH_RANK: 'process-match-rank',

  // Scout processors
  SCOUT_PROFILES: 'scout-profiles',

  // Video JD processors
  MONITOR_VIDEO_JD: 'monitor-video-jd',

  // Career insights processors
  PROCESS_CAREER_INSIGHT: 'process-insight',

  // Candidate comparison processors
  PROCESS_COMPARISON: 'process-comparison',
} as const;

export type ProcessorName = (typeof PROCESSOR_NAMES)[keyof typeof PROCESSOR_NAMES];
