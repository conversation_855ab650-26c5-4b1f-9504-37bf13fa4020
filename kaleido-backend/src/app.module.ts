import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { TerminusModule } from '@nestjs/terminus';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup';

import { AppController } from './app.controller';
import { AuthModule } from './auth/auth.module';
import { ApolloWebhookController } from './modules/webhooks/apollo-webhook.controller';
import slmConfig from './config/slm.config';
import * as entities from './entities';
import { ApprovalModule } from './modules/approval/approval.module';
import { AssessmentModule } from './modules/assessment/assessment.module';
import { AtsModule } from './modules/ats/ats.module';
import { AuthModule as AuthModuleController } from './modules/auth/auth.module';
import { CandidateModule } from './modules/candidate/candidate.module';
import { CareerInsightsModule } from './modules/career-insights/career-insights.module';
import { CandidateComparisonModule } from './modules/comparison/candidate-comparison.module';
import { CompaniesModule } from './modules/companies/companies.module';
import { CompanyModule } from './modules/company/company.module';
import { ContactModule } from './modules/contact/contact.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { EmailModule } from './modules/email/email.module';
import { FeatureFlagsModule } from './modules/feature-flags/feature-flags.module';
import { FeedbackModule } from './modules/feedback/feedback.module';
import { GraduateModule } from './modules/graduate/graduate.module';
import { HealthModule } from './modules/health/health.module';
import { JobSeekerModule } from './modules/job-seeker/job-seeker.module';
import { JobModule } from './modules/job/job.module';
import { NotificationModule } from './modules/notification/notification.module';
import { PaymentModule } from './modules/payment/payment.module';
import { RolesModule } from './modules/roles/roles.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { TestDashboardModule } from './modules/test-dashboard/test-dashboard.module';
import { VendorsModule } from './modules/vendors/vendors.module';
import { VideoJDModule } from './modules/video-jd/video-jd.module';
import { VideoResponseModule } from './modules/video-response/video-response.module';
import { WaitlistModule } from './modules/waitlist/waitlist.module';
import { WalletModule } from './modules/wallet/wallet.module';
import { QueueConfigModule } from './shared/modules/queue-config.module';
import { SharedModule } from './shared/shared.module';

@Module({
  imports: [
    SentryModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [slmConfig],
    }),
    AuthModule,
    TerminusModule,
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // First try to connect to postgres to create DB if needed
        const { Client } = require('pg');
        const client = new Client({
          host: configService.get('DB_HOST'),
          port: configService.get('DB_PORT'),
          user: configService.get('DB_USERNAME'),
          password: configService.get('DB_PASSWORD'),
          database: configService.get('DB_NAME') || 'postgres', // Connect to defaultdb instead of postgres on Digital Ocean
          ssl:
            configService.get('DB_SSL') === 'require'
              ? {
                  rejectUnauthorized: false,
                }
              : undefined,
        });

        try {
          await client.connect();
          const dbName = configService.get('DB_NAME') || '';
          const result = await client.query(`SELECT 1 FROM pg_database WHERE datname='${dbName}'`);
          if (result.rows.length === 0) {
            await client.query(`CREATE DATABASE "${dbName}"`);
          }
        } catch (error) {
          console.error('Error during database creation:', error);
          // Even if database creation fails, continue with connection
          // as the database likely already exists
        } finally {
          await client.end();
        }

        // Return TypeORM config
        return {
          type: 'postgres',
          host: configService.get('DB_HOST'),
          port: configService.get('DB_PORT'),
          username: configService.get('DB_USERNAME'),
          password: configService.get('DB_PASSWORD'),
          database: configService.get('DB_NAME') || '',
          entities: Object.values(entities),
          synchronize: false,
          // synchronize: configService.get('NODE_ENV') !== 'production',
          ...(configService.get('DB_SSL') === 'require'
            ? {
                ssl: {
                  rejectUnauthorized: false,
                },
                extra: {
                  ssl: {
                    rejectUnauthorized: false,
                  },
                },
              }
            : {}),
        };
      },
      inject: [ConfigService],
    }),
    QueueConfigModule,
    SharedModule,
    CompanyModule,
    CompaniesModule,
    JobModule,
    CandidateModule,
    VideoResponseModule,
    VideoJDModule,
    NotificationModule,
    RolesModule,
    VendorsModule,
    DashboardModule,
    JobSeekerModule,
    GraduateModule,
    HealthModule,
    EmailModule,
    ContactModule,
    AtsModule,
    ApprovalModule,
    AssessmentModule,
    AuthModuleController,
    SubscriptionModule,
    PaymentModule,
    FeatureFlagsModule,
    FeedbackModule,
    WaitlistModule,
    WalletModule,
    TestDashboardModule,
    CareerInsightsModule,
    CandidateComparisonModule,
  ],
  controllers: [AppController, ApolloWebhookController],
  providers: [
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useValue: {
        bodyParser: {
          json: { limit: '50mb' },
          urlencoded: { limit: '50mb', extended: true },
        },
      },
    },
  ],
})
export class AppModule {}
