import { MigrationInterface, QueryRunner } from "typeorm";

export class 1754137201829TempMigrationCheck.ts1754137203111 implements MigrationInterface {
    name = '1754137201829TempMigrationCheck.ts1754137203111'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP CONSTRAINT "fk_credit_purchases_company"`);
        await queryRunner.query(`ALTER TABLE "company_members" DROP CONSTRAINT "FK_company_members_company"`);
        await queryRunner.query(`ALTER TABLE "company_invitations" DROP CONSTRAINT "FK_company_invitations_company"`);
        await queryRunner.query(`ALTER TABLE "scouted_candidates" DROP CONSTRAINT "FK_scouted_candidates_job"`);
        await queryRunner.query(`ALTER TABLE "career_insights" DROP CONSTRAINT "fk_career_insights_job_seeker"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP CONSTRAINT "fk_comparison_job"`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP CONSTRAINT "FK_feedback_company"`);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" DROP CONSTRAINT "fk_credit_usage_history_company"`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_purchases_payment_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_purchases_stripe_session_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_seekers_email_correspondence_gin"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_seekers_activity_history_gin"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_WAITLIST_STATUS"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_WAITLIST_STRIPE_SESSION"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_email_correspondence_gin"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_is_favorited"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_is_shortlisted"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_scrapebook_notes"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_management_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_with_notes"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_SCOUTED_CANDIDATES_SEARCH_SOURCE"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_SCOUTED_CANDIDATES_COMPANY_CONTEXT"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_SCOUTED_CANDIDATES_APOLLO_METADATA"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_SCOUTED_CANDIDATES_COMPANY_JOB_SEARCH"`);
        await queryRunner.query(`DROP INDEX "public"."idx_contacts_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_FEATURE_FLAG_CREATED_BY"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_FEATURE_FLAG_UPDATED_BY"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_FEATURE_FLAG_IS_ACTIVE"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_FEATURE_FLAG_EXPIRES_AT"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_feedback_clientId"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_feedback_companyId"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_feedback_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_feedback_category"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_CREDIT_USAGE_COMPANY_CREATED"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_CREDIT_USAGE_COMPANY_ACTION"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP CONSTRAINT "chk_amount_paid_positive"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP CONSTRAINT "chk_credits_positive"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP CONSTRAINT "chk_total_credits_calculation"`);
        await queryRunner.query(`ALTER TABLE "career_insights" DROP CONSTRAINT "check_skill_gap_analysis"`);
        await queryRunner.query(`ALTER TABLE "career_insights" DROP CONSTRAINT "check_ai_insights"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP CONSTRAINT "candidate_comparisons_status_check"`);
        await queryRunner.query(`COMMENT ON TABLE "credit_purchases" IS NULL`);
        await queryRunner.query(`COMMENT ON TABLE "contacts" IS NULL`);
        await queryRunner.query(`COMMENT ON TABLE "feature_flags" IS NULL`);
        await queryRunner.query(`CREATE TYPE "public"."email_history_status_enum" AS ENUM('pending', 'sent', 'failed', 'delivered', 'bounced', 'opened', 'clicked')`);
        await queryRunner.query(`CREATE TABLE "email_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "template_id" character varying NOT NULL, "template_name" character varying NOT NULL, "recipient_email" character varying NOT NULL, "subject" character varying NOT NULL, "content" text, "status" "public"."email_history_status_enum" NOT NULL DEFAULT 'pending', "sent_by" character varying, "client_id" character varying, "external_id" character varying, "metadata" jsonb, "error" text, "sent_at" TIMESTAMP, "delivered_at" TIMESTAMP, "opened_at" TIMESTAMP, "clicked_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_abbda109218969deb4e9c90ac99" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "subscriptionLimits"`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP COLUMN "category"`);
        await queryRunner.query(`DROP TYPE "public"."feedback_category_enum"`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."companyId" IS NULL`);
        await queryRunner.query(`ALTER TYPE "public"."credit_package_enum" RENAME TO "credit_package_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."credit_purchases_packagetype_enum" AS ENUM('SMALL', 'MEDIUM', 'LARGE', 'EXTRA_LARGE', 'MEGA')`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "packageType" TYPE "public"."credit_purchases_packagetype_enum" USING "packageType"::"text"::"public"."credit_purchases_packagetype_enum"`);
        await queryRunner.query(`DROP TYPE "public"."credit_package_enum_old"`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."packageType" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."amountPaid" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."baseCredits" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."bonusCredits" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."totalCredits" IS NULL`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "paymentMethod" SET NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."paymentMethod" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."paymentId" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."stripeSessionId" IS NULL`);
        await queryRunner.query(`ALTER TYPE "public"."credit_purchase_status_enum" RENAME TO "credit_purchase_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."credit_purchases_status_enum" AS ENUM('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED')`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "status" TYPE "public"."credit_purchases_status_enum" USING "status"::"text"::"public"."credit_purchases_status_enum"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "status" SET DEFAULT 'PENDING'`);
        await queryRunner.query(`DROP TYPE "public"."credit_purchase_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "status" SET NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."status" IS NULL`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "metadata" SET NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."metadata" IS NULL`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_purchases_created_at"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP COLUMN "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "job_seekers" DROP CONSTRAINT "UQ_2fdf8401dc0dd4fbc082447aa23"`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ALTER COLUMN "skills" SET DEFAULT '{}'::text[]`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ALTER COLUMN "languages" SET DEFAULT '{}'::text[]`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ALTER COLUMN "myValues" SET DEFAULT '{}'::text[]`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ALTER COLUMN "onboardingProgress" SET DEFAULT '{"basicInfo":{"completed":false,"completedAt":null,"percentage":0,"requiredFields":["firstName","lastName","email"],"completedFields":[]},"professionalInfo":{"completed":false,"completedAt":null,"percentage":0,"requiredFields":["skills"],"completedFields":[]},"preferences":{"completed":false,"completedAt":null,"percentage":0,"requiredFields":["jobTypes","locations","remotePreference","desiredSalary.currency","desiredSalary.period","desiredSalary.min","desiredSalary.max"],"completedFields":[]},"additionalInfo":{"completed":false,"completedAt":null,"percentage":0,"requiredFields":[],"completedFields":[]},"overall":{"completed":false,"completedAt":null,"percentage":0},"lastUpdated":null}'`);
        await queryRunner.query(`ALTER TABLE "candidate_profiles" ALTER COLUMN "extractionMetadata" SET DEFAULT '[]'::jsonb`);
        await queryRunner.query(`ALTER TYPE "public"."candidate_status_enum" RENAME TO "candidate_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."candidate_status_enum" AS ENUM('NEW', 'APPLIED', 'MATCHED', 'MATCH_MODIFIED', 'CONTACTED', 'INTERESTED', 'NOT_INTERESTED', 'INTERVIEWING', 'OFFER_PENDING_APPROVAL', 'OFFER_APPROVED', 'OFFER_REJECTED', 'OFFER_EXTENDED', 'OFFER_ACCEPTED', 'OFFER_DECLINED', 'HIRE_PENDING_APPROVAL', 'HIRE_APPROVED', 'HIRED', 'REJECTED', 'WITHDRAWN', 'CULTURAL_FIT_ANSWERED', 'SHORTLISTED')`);
        await queryRunner.query(`ALTER TABLE "candidate_applications" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidate_applications" ALTER COLUMN "status" TYPE "public"."candidate_status_enum" USING "status"::"text"::"public"."candidate_status_enum"`);
        await queryRunner.query(`ALTER TABLE "candidate_applications" ALTER COLUMN "status" SET DEFAULT 'NEW'`);
        await queryRunner.query(`DROP TYPE "public"."candidate_status_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."candidate_status_enum" RENAME TO "candidate_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."candidate_status_enum" AS ENUM('NEW', 'APPLIED', 'MATCHED', 'MATCH_MODIFIED', 'CONTACTED', 'INTERESTED', 'NOT_INTERESTED', 'INTERVIEWING', 'OFFER_PENDING_APPROVAL', 'OFFER_APPROVED', 'OFFER_REJECTED', 'OFFER_EXTENDED', 'OFFER_ACCEPTED', 'OFFER_DECLINED', 'HIRE_PENDING_APPROVAL', 'HIRE_APPROVED', 'HIRED', 'REJECTED', 'WITHDRAWN', 'CULTURAL_FIT_ANSWERED', 'SHORTLISTED')`);
        await queryRunner.query(`ALTER TABLE "candidate_evaluations" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidate_evaluations" ALTER COLUMN "status" TYPE "public"."candidate_status_enum" USING "status"::"text"::"public"."candidate_status_enum"`);
        await queryRunner.query(`ALTER TABLE "candidate_evaluations" ALTER COLUMN "status" SET DEFAULT 'NEW'`);
        await queryRunner.query(`DROP TYPE "public"."candidate_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "first_name"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "first_name" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "last_name"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "last_name" character varying NOT NULL`);
        await queryRunner.query(`DROP INDEX "public"."idx_recruitment_assessments_email"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "email"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "email" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "phone_number"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "phone_number" character varying`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "position"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "position" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "company"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "company" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "maturity_level"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "maturity_level" character varying`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ALTER COLUMN "is_completed" SET NOT NULL`);
        await queryRunner.query(`DROP INDEX "public"."idx_recruitment_assessments_created_at"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TYPE "public"."company_member_role_enum" RENAME TO "company_member_role_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."company_members_role_enum" AS ENUM('owner', 'admin', 'member', 'viewer')`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "role" TYPE "public"."company_members_role_enum" USING "role"::"text"::"public"."company_members_role_enum"`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "role" SET DEFAULT 'member'`);
        await queryRunner.query(`DROP TYPE "public"."company_member_role_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."company_member_status_enum" RENAME TO "company_member_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."company_members_status_enum" AS ENUM('active', 'invited', 'suspended')`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "status" TYPE "public"."company_members_status_enum" USING "status"::"text"::"public"."company_members_status_enum"`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "status" SET DEFAULT 'invited'`);
        await queryRunner.query(`DROP TYPE "public"."company_member_status_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."company_member_role_enum" RENAME TO "company_member_role_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."company_invitations_role_enum" AS ENUM('owner', 'admin', 'member', 'viewer')`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "role" TYPE "public"."company_invitations_role_enum" USING "role"::"text"::"public"."company_invitations_role_enum"`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "role" SET DEFAULT 'member'`);
        await queryRunner.query(`DROP TYPE "public"."company_member_role_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."invitation_status_enum" RENAME TO "invitation_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."company_invitations_status_enum" AS ENUM('pending', 'accepted', 'expired', 'cancelled')`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "status" TYPE "public"."company_invitations_status_enum" USING "status"::"text"::"public"."company_invitations_status_enum"`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."invitation_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "isApproved" SET DEFAULT false`);
        await queryRunner.query(`ALTER TYPE "public"."subscription_plan_enum" RENAME TO "subscription_plan_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."companies_subscriptionplan_enum" AS ENUM('free', 'startup', 'starter', 'professional', 'enterprise')`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionPlan" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionPlan" TYPE "public"."companies_subscriptionplan_enum" USING "subscriptionPlan"::"text"::"public"."companies_subscriptionplan_enum"`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionPlan" SET DEFAULT 'free'`);
        await queryRunner.query(`DROP TYPE "public"."subscription_plan_enum_old"`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionCredits" SET DEFAULT '{"totalCredits":5,"usedCredits":0,"remainingCredits":5,"monthlyAllocation":5,"lastResetDate":"2025-08-02T12:20:05.985Z","videoJdMaxDuration":30,"atsIntegration":"basic","databaseRetentionMonths":3}'`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionAutoRenew" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "subscriptionPaymentMethod"`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "subscriptionPaymentMethod" character varying`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "subscriptionPaymentId"`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "subscriptionPaymentId" character varying`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "autoJoinEnabled" SET NOT NULL`);
        await queryRunner.query(`ALTER TYPE "public"."videojd_type_enum" RENAME TO "videojd_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."videoJDs_type_enum" AS ENUM('AI_AVATAR', 'LIVE_RECORDING')`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "type" TYPE "public"."videoJDs_type_enum" USING "type"::"text"::"public"."videoJDs_type_enum"`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "type" SET DEFAULT 'AI_AVATAR'`);
        await queryRunner.query(`DROP TYPE "public"."videojd_type_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."videojd_virtualbackgroundtype_enum" RENAME TO "videojd_virtualbackgroundtype_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."videoJDs_virtualbackgroundtype_enum" AS ENUM('NONE', 'BLUR', 'IMAGE')`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "virtualBackgroundType" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "virtualBackgroundType" TYPE "public"."videoJDs_virtualbackgroundtype_enum" USING "virtualBackgroundType"::"text"::"public"."videoJDs_virtualbackgroundtype_enum"`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "virtualBackgroundType" SET DEFAULT 'NONE'`);
        await queryRunner.query(`DROP TYPE "public"."videojd_virtualbackgroundtype_enum_old"`);
        await queryRunner.query(`ALTER TABLE "waitlist" ADD CONSTRAINT "UQ_2221cffeeb64bff14201bd5b3de" UNIQUE ("email")`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "fullName" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "phoneNumber" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "company" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "createdAt" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "updatedAt" SET DEFAULT now()`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_job_status_tier"`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "skills" SET DEFAULT '{}'::text[]`);
        await queryRunner.query(`ALTER TYPE "public"."candidates_status_enum" RENAME TO "candidates_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."candidates_status_enum" AS ENUM('NEW', 'APPLIED', 'MATCHED', 'MATCH_MODIFIED', 'CONTACTED', 'INTERESTED', 'NOT_INTERESTED', 'INTERVIEWING', 'OFFER_PENDING_APPROVAL', 'OFFER_APPROVED', 'OFFER_REJECTED', 'OFFER_EXTENDED', 'OFFER_ACCEPTED', 'OFFER_DECLINED', 'HIRE_PENDING_APPROVAL', 'HIRE_APPROVED', 'HIRED', 'REJECTED', 'WITHDRAWN', 'CULTURAL_FIT_ANSWERED', 'SHORTLISTED')`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "status" TYPE "public"."candidates_status_enum" USING "status"::"text"::"public"."candidates_status_enum"`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "status" SET DEFAULT 'NEW'`);
        await queryRunner.query(`DROP TYPE "public"."candidates_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "interviews" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "statusHistory" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "documents" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "communicationLogs" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "tasks" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "appliedJobs" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "activityHistory" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "extractionMetadata" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "clientSpecificData" DROP DEFAULT`);
        await queryRunner.query(`COMMENT ON COLUMN "candidates"."isFavorited" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "candidates"."isShortlisted" IS NULL`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "scrapebookNotes" DROP NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "candidates"."scrapebookNotes" IS NULL`);
        await queryRunner.query(`ALTER TABLE "scouted_candidates" ALTER COLUMN "skills" SET NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "scouted_candidates"."searchSource" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "scouted_candidates"."apolloMetadata" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "scouted_candidates"."companyContext" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "scouted_candidates"."searchRank" IS NULL`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "clientId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "createdAt" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "updatedAt" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TYPE "public"."insight_type" RENAME TO "insight_type_old"`);
        await queryRunner.query(`CREATE TYPE "public"."career_insights_type_enum" AS ENUM('SKILL_GAP_ANALYSIS', 'CAREER_PATH_RECOMMENDATION', 'MARKET_TREND_ANALYSIS', 'ROLE_TRANSITION_GUIDANCE', 'INDUSTRY_OUTLOOK', 'COMPENSATION_BENCHMARK')`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "type" TYPE "public"."career_insights_type_enum" USING "type"::"text"::"public"."career_insights_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."insight_type_old"`);
        await queryRunner.query(`ALTER TYPE "public"."insight_status" RENAME TO "insight_status_old"`);
        await queryRunner.query(`CREATE TYPE "public"."career_insights_status_enum" AS ENUM('DRAFT', 'PROCESSING', 'READY', 'ARCHIVED')`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "status" TYPE "public"."career_insights_status_enum" USING "status"::"text"::"public"."career_insights_status_enum"`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "status" SET DEFAULT 'DRAFT'`);
        await queryRunner.query(`DROP TYPE "public"."insight_status_old"`);
        await queryRunner.query(`DROP INDEX "public"."idx_comparison_client_job"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "clientId"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "clientId" character varying`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ALTER COLUMN "createdAt" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ALTER COLUMN "updatedAt" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "candidateIds"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "candidateIds" json NOT NULL`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "comparisonCriteria"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "comparisonCriteria" json NOT NULL`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "comparisonResults"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "comparisonResults" json`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "visualData"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "visualData" json`);
        await queryRunner.query(`DROP INDEX "public"."idx_comparison_status"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "status"`);
        await queryRunner.query(`CREATE TYPE "public"."candidate_comparisons_status_enum" AS ENUM('pending', 'completed', 'failed')`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "status" "public"."candidate_comparisons_status_enum" NOT NULL DEFAULT 'pending'`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "performedBy"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "performedBy" character varying`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "comparisonType"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "comparisonType" character varying`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "metadata"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "metadata" json`);
        await queryRunner.query(`ALTER TYPE "public"."contact_status" RENAME TO "contact_status_old"`);
        await queryRunner.query(`CREATE TYPE "public"."contacts_status_enum" AS ENUM('new', 'in_progress', 'resolved', 'closed')`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "status" TYPE "public"."contacts_status_enum" USING "status"::"text"::"public"."contacts_status_enum"`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "status" SET DEFAULT 'new'`);
        await queryRunner.query(`DROP TYPE "public"."contact_status_old"`);
        await queryRunner.query(`COMMENT ON COLUMN "contacts"."status" IS NULL`);
        await queryRunner.query(`ALTER TYPE "public"."contact_priority" RENAME TO "contact_priority_old"`);
        await queryRunner.query(`CREATE TYPE "public"."contacts_priority_enum" AS ENUM('low', 'medium', 'high', 'urgent')`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "priority" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "priority" TYPE "public"."contacts_priority_enum" USING "priority"::"text"::"public"."contacts_priority_enum"`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "priority" SET DEFAULT 'medium'`);
        await queryRunner.query(`DROP TYPE "public"."contact_priority_old"`);
        await queryRunner.query(`COMMENT ON COLUMN "contacts"."priority" IS NULL`);
        await queryRunner.query(`ALTER TYPE "public"."contact_source" RENAME TO "contact_source_old"`);
        await queryRunner.query(`CREATE TYPE "public"."contacts_source_enum" AS ENUM('website', 'email', 'phone', 'social_media', 'other')`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "source" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "source" TYPE "public"."contacts_source_enum" USING "source"::"text"::"public"."contacts_source_enum"`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "source" SET DEFAULT 'website'`);
        await queryRunner.query(`DROP TYPE "public"."contact_source_old"`);
        await queryRunner.query(`COMMENT ON COLUMN "contacts"."source" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "contacts"."metadata" IS NULL`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "created_at" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "updated_at" SET DEFAULT now()`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."id" IS NULL`);
        await queryRunner.query(`DROP INDEX "public"."IDX_FEATURE_FLAG_NAME"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP CONSTRAINT "feature_flags_name_key"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "name"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "name" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD CONSTRAINT "UQ_d5a8bb2df6ade7ced134d957963" UNIQUE ("name")`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."value" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."description" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."isActive" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."expiresAt" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."userRoles" IS NULL`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "createdBy" character varying`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "updatedBy"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "updatedBy" character varying`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "feedback" ALTER COLUMN "title" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" ALTER COLUMN "createdAt" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" ALTER COLUMN "updatedAt" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TYPE "public"."credit_action_type_enum" RENAME TO "credit_action_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."credit_usage_history_actiontype_enum" AS ENUM('resumeUpload', 'scout', 'matchRank', 'videoJd', 'cultureFitQuestions', 'JD_GENERATION', 'LINKEDIN_SCOUT', 'APOLLO_SCOUT', 'PDL_SCOUT')`);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" ALTER COLUMN "actionType" TYPE "public"."credit_usage_history_actiontype_enum" USING "actionType"::"text"::"public"."credit_usage_history_actiontype_enum"`);
        await queryRunner.query(`DROP TYPE "public"."credit_action_type_enum_old"`);
        await queryRunner.query(`CREATE INDEX "idx_credit_purchases_payment_method" ON "credit_purchases" ("paymentMethod") `);
        await queryRunner.query(`CREATE INDEX "idx_credit_purchases_created_at" ON "credit_purchases" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "idx_credit_purchases_package_type" ON "credit_purchases" ("packageType") `);
        await queryRunner.query(`CREATE INDEX "idx_job_seekers_role" ON "job_seekers" ("role") `);
        await queryRunner.query(`CREATE INDEX "idx_job_seekers_location" ON "job_seekers" ("location") `);
        await queryRunner.query(`CREATE INDEX "idx_job_seekers_client_id" ON "job_seekers" ("clientId") `);
        await queryRunner.query(`CREATE INDEX "idx_job_seekers_email" ON "job_seekers" ("email") `);
        await queryRunner.query(`CREATE INDEX "idx_job_seekers_user_id" ON "job_seekers" ("userId") `);
        await queryRunner.query(`CREATE INDEX "idx_job_applications_status" ON "job_applications" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_job_applications_job_seeker_id" ON "job_applications" ("jobSeekerId") `);
        await queryRunner.query(`CREATE INDEX "idx_job_applications_job_id" ON "job_applications" ("jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_video_responses_status" ON "video_responses" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_video_responses_candidate_id" ON "video_responses" ("candidateId") `);
        await queryRunner.query(`CREATE INDEX "idx_video_responses_job_id" ON "video_responses" ("jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_profiles_full_name" ON "candidate_profiles" ("fullName") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_profiles_email" ON "candidate_profiles" ("email") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_applications_tier" ON "candidate_applications" ("tier") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_applications_contacted" ON "candidate_applications" ("contacted") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_applications_status" ON "candidate_applications" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_applications_job_id" ON "candidate_applications" ("jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_applications_profile_id" ON "candidate_applications" ("profileId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_evaluations_contacted" ON "candidate_evaluations" ("contacted") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_evaluations_match_score" ON "candidate_evaluations" ("matchScore") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_evaluations_tier" ON "candidate_evaluations" ("tier") `);
        await queryRunner.query(`CREATE INDEX "idx_candidate_evaluations_status" ON "candidate_evaluations" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_source" ON "jobs" ("source") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_external_id" ON "jobs" ("externalId") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_is_graduate_role" ON "jobs" ("isGraduateRole") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_created_at" ON "jobs" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_client_status_published" ON "jobs" ("clientId", "status", "isPublished") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_experience_level" ON "jobs" ("experienceLevel") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_is_published" ON "jobs" ("isPublished") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_status" ON "jobs" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_jobs_client_id" ON "jobs" ("clientId") `);
        await queryRunner.query(`CREATE INDEX "idx_offers_responded_at" ON "offers" ("respondedAt") `);
        await queryRunner.query(`CREATE INDEX "idx_offers_sent_at" ON "offers" ("sentAt") `);
        await queryRunner.query(`CREATE INDEX "idx_offers_status" ON "offers" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_offers_job_id" ON "offers" ("jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_offers_candidate_id" ON "offers" ("candidateId") `);
        await queryRunner.query(`CREATE INDEX "idx_recruitment_assessments_created_at" ON "recruitment_assessments" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "idx_recruitment_assessments_email" ON "recruitment_assessments" ("email") `);
        await queryRunner.query(`CREATE INDEX "idx_companies_industry" ON "companies" ("industry") `);
        await queryRunner.query(`CREATE INDEX "idx_companies_created_at" ON "companies" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "idx_notifications_completed" ON "notifications" ("completed") `);
        await queryRunner.query(`CREATE INDEX "idx_notifications_client_read_created" ON "notifications" ("clientId", "read", "createdAt") `);
        await queryRunner.query(`CREATE INDEX "idx_notifications_type" ON "notifications" ("type") `);
        await queryRunner.query(`CREATE INDEX "idx_notifications_read" ON "notifications" ("read") `);
        await queryRunner.query(`CREATE INDEX "idx_notifications_client_id" ON "notifications" ("clientId") `);
        await queryRunner.query(`CREATE INDEX "idx_notifications_job_id" ON "notifications" ("jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_user_roles_role" ON "user_roles" ("role") `);
        await queryRunner.query(`CREATE INDEX "idx_user_roles_client_id" ON "user_roles" ("clientId") `);
        await queryRunner.query(`CREATE INDEX "idx_video_jds_virtual_background_type" ON "videoJDs" ("virtualBackgroundType") `);
        await queryRunner.query(`CREATE INDEX "idx_video_jds_client_id" ON "videoJDs" ("clientId") `);
        await queryRunner.query(`CREATE INDEX "idx_video_jds_status" ON "videoJDs" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_video_jds_job_id" ON "videoJDs" ("jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_waitlist_created_at" ON "waitlist" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "idx_waitlist_role" ON "waitlist" ("role") `);
        await queryRunner.query(`CREATE INDEX "idx_waitlist_status" ON "waitlist" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_graduates_has_completed_onboarding" ON "graduates" ("hasCompletedOnboarding") `);
        await queryRunner.query(`CREATE INDEX "idx_graduates_graduation_year" ON "graduates" ("graduationYear") `);
        await queryRunner.query(`CREATE INDEX "idx_graduates_university" ON "graduates" ("university") `);
        await queryRunner.query(`CREATE INDEX "idx_graduates_final_approval" ON "graduates" ("finalApproval") `);
        await queryRunner.query(`CREATE INDEX "idx_graduates_role" ON "graduates" ("role") `);
        await queryRunner.query(`CREATE INDEX "idx_graduates_email" ON "graduates" ("email") `);
        await queryRunner.query(`CREATE INDEX "idx_graduates_user_id" ON "graduates" ("userId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_userid" ON "candidates" ("userId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_clientid" ON "candidates" ("clientId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_fullname" ON "candidates" ("fullName") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_years_experience" ON "candidates" ("yearsOfExperience") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_contacted" ON "candidates" ("contacted") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_external_id" ON "candidates" ("externalId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_graduate_id" ON "candidates" ("graduateId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_job_seeker_id" ON "candidates" ("jobSeekerId") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_source" ON "candidates" ("source") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_tier" ON "candidates" ("tier") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_status" ON "candidates" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_email" ON "candidates" ("email") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_job_status_tier" ON "candidates" ("jobId", "status", "tier") `);
        await queryRunner.query(`CREATE INDEX "idx_approvals_due_date" ON "approvals" ("dueDate") `);
        await queryRunner.query(`CREATE INDEX "idx_approvals_requestor_id" ON "approvals" ("requestorId") `);
        await queryRunner.query(`CREATE INDEX "idx_approvals_type" ON "approvals" ("type") `);
        await queryRunner.query(`CREATE INDEX "idx_approvals_status" ON "approvals" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_approvals_job_id" ON "approvals" ("jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_approvals_candidate_id" ON "approvals" ("candidateId") `);
        await queryRunner.query(`CREATE INDEX "idx_approval_steps_order" ON "approval_steps" ("order") `);
        await queryRunner.query(`CREATE INDEX "idx_approval_steps_approver_id" ON "approval_steps" ("approverId") `);
        await queryRunner.query(`CREATE INDEX "idx_approval_steps_status" ON "approval_steps" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_approval_steps_approval_id" ON "approval_steps" ("approvalId") `);
        await queryRunner.query(`CREATE INDEX "idx_scouted_candidates_search_source" ON "scouted_candidates" ("searchSource") `);
        await queryRunner.query(`CREATE INDEX "idx_scouted_candidates_is_added" ON "scouted_candidates" ("isAdded") `);
        await queryRunner.query(`CREATE INDEX "idx_scouted_candidates_job_id" ON "scouted_candidates" ("jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_comparison_status" ON "candidate_comparisons" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_comparison_client_job" ON "candidate_comparisons" ("clientId", "jobId") `);
        await queryRunner.query(`CREATE INDEX "idx_contacts_company" ON "contacts" ("company") `);
        await queryRunner.query(`CREATE INDEX "idx_contacts_source" ON "contacts" ("source") `);
        await queryRunner.query(`CREATE INDEX "idx_contacts_priority" ON "contacts" ("priority") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_FEATURE_FLAG_NAME" ON "feature_flags" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_e949176341d007b08b2eae2233" ON "feature_flags" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_94194c806c057086e6dc5c2581" ON "feature_flags" ("updatedBy") `);
        await queryRunner.query(`CREATE INDEX "idx_feature_flags_name" ON "feature_flags" ("name") `);
        await queryRunner.query(`CREATE INDEX "idx_feature_flags_expires_at" ON "feature_flags" ("expiresAt") `);
        await queryRunner.query(`CREATE INDEX "idx_feature_flags_is_active" ON "feature_flags" ("isActive") `);
        await queryRunner.query(`CREATE INDEX "IDX_2db3acb4d27ec2cbf750cf51bc" ON "feedback" ("clientId") `);
        await queryRunner.query(`CREATE INDEX "idx_feedback_importance" ON "feedback" ("importance") `);
        await queryRunner.query(`CREATE INDEX "idx_feedback_status" ON "feedback" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_feedback_company_id" ON "feedback" ("companyId") `);
        await queryRunner.query(`CREATE INDEX "idx_credit_usage_history_performed_by" ON "credit_usage_history" ("performedBy") `);
        await queryRunner.query(`CREATE INDEX "idx_credit_usage_history_related_entity" ON "credit_usage_history" ("relatedEntityType", "relatedEntityId") `);
        await queryRunner.query(`CREATE INDEX "idx_credit_usage_history_action_type" ON "credit_usage_history" ("actionType") `);
        await queryRunner.query(`CREATE INDEX "IDX_2772ea9e940858c1aedb0a0d17" ON "credit_usage_history" ("companyId", "actionType") `);
        await queryRunner.query(`CREATE INDEX "IDX_e3d05f62de4053a9655af53056" ON "credit_usage_history" ("companyId", "createdAt") `);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD CONSTRAINT "FK_0acc3772280b1e6cc65cf418cc4" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "company_members" ADD CONSTRAINT "FK_ee30e433648d2a45dfd38cfb366" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ADD CONSTRAINT "FK_9955c6fc3e8c37d502265ce846d" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "scouted_candidates" ADD CONSTRAINT "FK_5eeb929e96edfd3a365408c6147" FOREIGN KEY ("jobId") REFERENCES "jobs"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "career_insights" ADD CONSTRAINT "FK_dcf0a3d0f9ade66a9c3b720b08e" FOREIGN KEY ("jobSeekerId") REFERENCES "job_seekers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD CONSTRAINT "FK_1f16d82e88d3c3a512f5ba60d02" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" ADD CONSTRAINT "FK_87eed15ec5fa5d639930e3263e3" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "credit_usage_history" DROP CONSTRAINT "FK_87eed15ec5fa5d639930e3263e3"`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP CONSTRAINT "FK_1f16d82e88d3c3a512f5ba60d02"`);
        await queryRunner.query(`ALTER TABLE "career_insights" DROP CONSTRAINT "FK_dcf0a3d0f9ade66a9c3b720b08e"`);
        await queryRunner.query(`ALTER TABLE "scouted_candidates" DROP CONSTRAINT "FK_5eeb929e96edfd3a365408c6147"`);
        await queryRunner.query(`ALTER TABLE "company_invitations" DROP CONSTRAINT "FK_9955c6fc3e8c37d502265ce846d"`);
        await queryRunner.query(`ALTER TABLE "company_members" DROP CONSTRAINT "FK_ee30e433648d2a45dfd38cfb366"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP CONSTRAINT "FK_0acc3772280b1e6cc65cf418cc4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e3d05f62de4053a9655af53056"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2772ea9e940858c1aedb0a0d17"`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_usage_history_action_type"`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_usage_history_related_entity"`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_usage_history_performed_by"`);
        await queryRunner.query(`DROP INDEX "public"."idx_feedback_company_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_feedback_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_feedback_importance"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2db3acb4d27ec2cbf750cf51bc"`);
        await queryRunner.query(`DROP INDEX "public"."idx_feature_flags_is_active"`);
        await queryRunner.query(`DROP INDEX "public"."idx_feature_flags_expires_at"`);
        await queryRunner.query(`DROP INDEX "public"."idx_feature_flags_name"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_94194c806c057086e6dc5c2581"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e949176341d007b08b2eae2233"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_FEATURE_FLAG_NAME"`);
        await queryRunner.query(`DROP INDEX "public"."idx_contacts_priority"`);
        await queryRunner.query(`DROP INDEX "public"."idx_contacts_source"`);
        await queryRunner.query(`DROP INDEX "public"."idx_contacts_company"`);
        await queryRunner.query(`DROP INDEX "public"."idx_comparison_client_job"`);
        await queryRunner.query(`DROP INDEX "public"."idx_comparison_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_scouted_candidates_job_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_scouted_candidates_is_added"`);
        await queryRunner.query(`DROP INDEX "public"."idx_scouted_candidates_search_source"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approval_steps_approval_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approval_steps_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approval_steps_approver_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approval_steps_order"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approvals_candidate_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approvals_job_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approvals_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approvals_type"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approvals_requestor_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_approvals_due_date"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_job_status_tier"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_email"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_tier"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_source"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_job_seeker_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_graduate_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_external_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_contacted"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_years_experience"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_fullname"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_clientid"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidates_userid"`);
        await queryRunner.query(`DROP INDEX "public"."idx_graduates_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_graduates_email"`);
        await queryRunner.query(`DROP INDEX "public"."idx_graduates_role"`);
        await queryRunner.query(`DROP INDEX "public"."idx_graduates_final_approval"`);
        await queryRunner.query(`DROP INDEX "public"."idx_graduates_university"`);
        await queryRunner.query(`DROP INDEX "public"."idx_graduates_graduation_year"`);
        await queryRunner.query(`DROP INDEX "public"."idx_graduates_has_completed_onboarding"`);
        await queryRunner.query(`DROP INDEX "public"."idx_waitlist_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_waitlist_role"`);
        await queryRunner.query(`DROP INDEX "public"."idx_waitlist_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."idx_video_jds_job_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_video_jds_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_video_jds_client_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_video_jds_virtual_background_type"`);
        await queryRunner.query(`DROP INDEX "public"."idx_user_roles_client_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_user_roles_role"`);
        await queryRunner.query(`DROP INDEX "public"."idx_notifications_job_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_notifications_client_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_notifications_read"`);
        await queryRunner.query(`DROP INDEX "public"."idx_notifications_type"`);
        await queryRunner.query(`DROP INDEX "public"."idx_notifications_client_read_created"`);
        await queryRunner.query(`DROP INDEX "public"."idx_notifications_completed"`);
        await queryRunner.query(`DROP INDEX "public"."idx_companies_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."idx_companies_industry"`);
        await queryRunner.query(`DROP INDEX "public"."idx_recruitment_assessments_email"`);
        await queryRunner.query(`DROP INDEX "public"."idx_recruitment_assessments_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."idx_offers_candidate_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_offers_job_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_offers_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_offers_sent_at"`);
        await queryRunner.query(`DROP INDEX "public"."idx_offers_responded_at"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_client_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_is_published"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_experience_level"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_client_status_published"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_is_graduate_role"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_external_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_jobs_source"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_evaluations_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_evaluations_tier"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_evaluations_match_score"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_evaluations_contacted"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_applications_profile_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_applications_job_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_applications_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_applications_contacted"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_applications_tier"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_profiles_email"`);
        await queryRunner.query(`DROP INDEX "public"."idx_candidate_profiles_full_name"`);
        await queryRunner.query(`DROP INDEX "public"."idx_video_responses_job_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_video_responses_candidate_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_video_responses_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_applications_job_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_applications_job_seeker_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_applications_status"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_seekers_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_seekers_email"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_seekers_client_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_seekers_location"`);
        await queryRunner.query(`DROP INDEX "public"."idx_job_seekers_role"`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_purchases_package_type"`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_purchases_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."idx_credit_purchases_payment_method"`);
        await queryRunner.query(`CREATE TYPE "public"."credit_action_type_enum_old" AS ENUM('resumeUpload', 'scout', 'matchRank', 'videoJd', 'cultureFitQuestions', 'JD_GENERATION', 'LINKEDIN_SCOUT', 'APOLLO_SCOUT', 'PDL_SCOUT')`);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" ALTER COLUMN "actionType" TYPE "public"."credit_action_type_enum_old" USING "actionType"::"text"::"public"."credit_action_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."credit_usage_history_actiontype_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."credit_action_type_enum_old" RENAME TO "credit_action_type_enum"`);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" ALTER COLUMN "updatedAt" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" ALTER COLUMN "createdAt" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "feedback" ALTER COLUMN "title" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "updatedBy"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "updatedBy" character varying(255)`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "createdBy" character varying(255)`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."userRoles" IS 'Array of user roles that can see this feature flag'`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."expiresAt" IS 'Optional timestamp when the feature flag expires'`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."isActive" IS 'Whether the feature flag is currently active'`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."description" IS 'Optional description of what this feature flag controls'`);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."value" IS 'The value of the feature flag (boolean, string, number, or null)'`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP CONSTRAINT "UQ_d5a8bb2df6ade7ced134d957963"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" DROP COLUMN "name"`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD "name" character varying(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "feature_flags" ADD CONSTRAINT "feature_flags_name_key" UNIQUE ("name")`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_FEATURE_FLAG_NAME" ON "feature_flags" ("name") `);
        await queryRunner.query(`COMMENT ON COLUMN "feature_flags"."id" IS 'Unique identifier for the feature flag'`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`COMMENT ON COLUMN "contacts"."metadata" IS 'Additional flexible data stored as JSON'`);
        await queryRunner.query(`COMMENT ON COLUMN "contacts"."source" IS 'Source channel where the contact came from'`);
        await queryRunner.query(`CREATE TYPE "public"."contact_source_old" AS ENUM('website', 'email', 'phone', 'social_media', 'other')`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "source" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "source" TYPE "public"."contact_source_old" USING "source"::"text"::"public"."contact_source_old"`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "source" SET DEFAULT 'website'`);
        await queryRunner.query(`DROP TYPE "public"."contacts_source_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."contact_source_old" RENAME TO "contact_source"`);
        await queryRunner.query(`COMMENT ON COLUMN "contacts"."priority" IS 'Priority level of the contact inquiry'`);
        await queryRunner.query(`CREATE TYPE "public"."contact_priority_old" AS ENUM('low', 'medium', 'high', 'urgent')`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "priority" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "priority" TYPE "public"."contact_priority_old" USING "priority"::"text"::"public"."contact_priority_old"`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "priority" SET DEFAULT 'medium'`);
        await queryRunner.query(`DROP TYPE "public"."contacts_priority_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."contact_priority_old" RENAME TO "contact_priority"`);
        await queryRunner.query(`COMMENT ON COLUMN "contacts"."status" IS 'Current status of the contact inquiry'`);
        await queryRunner.query(`CREATE TYPE "public"."contact_status_old" AS ENUM('new', 'in_progress', 'resolved', 'closed')`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "status" TYPE "public"."contact_status_old" USING "status"::"text"::"public"."contact_status_old"`);
        await queryRunner.query(`ALTER TABLE "contacts" ALTER COLUMN "status" SET DEFAULT 'new'`);
        await queryRunner.query(`DROP TYPE "public"."contacts_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."contact_status_old" RENAME TO "contact_status"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "metadata"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "metadata" jsonb`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "comparisonType"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "comparisonType" character varying(255)`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "performedBy"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "performedBy" character varying(255)`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."candidate_comparisons_status_enum"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "status" character varying(20) DEFAULT 'pending'`);
        await queryRunner.query(`CREATE INDEX "idx_comparison_status" ON "candidate_comparisons" ("status") `);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "visualData"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "visualData" jsonb`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "comparisonResults"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "comparisonResults" jsonb`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "comparisonCriteria"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "comparisonCriteria" jsonb NOT NULL`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "candidateIds"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "candidateIds" jsonb NOT NULL`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ALTER COLUMN "updatedAt" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ALTER COLUMN "createdAt" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" DROP COLUMN "clientId"`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD "clientId" character varying(255) NOT NULL`);
        await queryRunner.query(`CREATE INDEX "idx_comparison_client_job" ON "candidate_comparisons" ("clientId", "jobId") `);
        await queryRunner.query(`CREATE TYPE "public"."insight_status_old" AS ENUM('DRAFT', 'PROCESSING', 'READY', 'ARCHIVED')`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "status" TYPE "public"."insight_status_old" USING "status"::"text"::"public"."insight_status_old"`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "status" SET DEFAULT 'DRAFT'`);
        await queryRunner.query(`DROP TYPE "public"."career_insights_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."insight_status_old" RENAME TO "insight_status"`);
        await queryRunner.query(`CREATE TYPE "public"."insight_type_old" AS ENUM('SKILL_GAP_ANALYSIS', 'CAREER_PATH_RECOMMENDATION', 'MARKET_TREND_ANALYSIS', 'ROLE_TRANSITION_GUIDANCE', 'INDUSTRY_OUTLOOK', 'COMPENSATION_BENCHMARK')`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "type" TYPE "public"."insight_type_old" USING "type"::"text"::"public"."insight_type_old"`);
        await queryRunner.query(`DROP TYPE "public"."career_insights_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."insight_type_old" RENAME TO "insight_type"`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "updatedAt" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "createdAt" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "career_insights" ALTER COLUMN "clientId" SET NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "scouted_candidates"."searchRank" IS 'Position in search results for diversity tracking and result quality analysis'`);
        await queryRunner.query(`COMMENT ON COLUMN "scouted_candidates"."companyContext" IS 'Company ID for company-specific caching and search optimization'`);
        await queryRunner.query(`COMMENT ON COLUMN "scouted_candidates"."apolloMetadata" IS 'Metadata for Apollo searches including profileId, searchTimestamp, searchPage, searchStrategy, and originalQuery'`);
        await queryRunner.query(`COMMENT ON COLUMN "scouted_candidates"."searchSource" IS 'Source of the candidate search: APOLLO_API, APOLLO_ENHANCED_CACHE, LINKEDIN_SCOUT'`);
        await queryRunner.query(`ALTER TABLE "scouted_candidates" ALTER COLUMN "skills" DROP NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "candidates"."scrapebookNotes" IS 'Page-based notes and sticky notes. Structure: { [pageId]: [{ id, content, createdAt, updatedAt, createdBy, type, color?, position?, tags?, isPrivate? }] }'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "scrapebookNotes" SET NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "candidates"."isShortlisted" IS 'Whether the candidate is shortlisted by the company'`);
        await queryRunner.query(`COMMENT ON COLUMN "candidates"."isFavorited" IS 'Whether the candidate is marked as favorite by the company'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "clientSpecificData" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "extractionMetadata" SET DEFAULT '[]'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "activityHistory" SET DEFAULT '[]'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "appliedJobs" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "tasks" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "communicationLogs" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "documents" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "statusHistory" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "interviews" SET DEFAULT '{}'`);
        await queryRunner.query(`CREATE TYPE "public"."candidates_status_enum_old" AS ENUM('NEW', 'MATCHED', 'CONTACTED', 'INTERESTED', 'NOT_INTERESTED', 'INTERVIEWING', 'OFFER_PENDING_APPROVAL', 'OFFER_APPROVED', 'OFFER_REJECTED', 'OFFER_EXTENDED', 'OFFER_ACCEPTED', 'OFFER_DECLINED', 'HIRE_PENDING_APPROVAL', 'HIRE_APPROVED', 'HIRED', 'REJECTED', 'WITHDRAWN', 'CULTURAL_FIT_ANSWERED', 'SHORTLISTED', 'APPLIED')`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "status" TYPE "public"."candidates_status_enum_old" USING "status"::"text"::"public"."candidates_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "status" SET DEFAULT 'NEW'`);
        await queryRunner.query(`DROP TYPE "public"."candidates_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."candidates_status_enum_old" RENAME TO "candidates_status_enum"`);
        await queryRunner.query(`ALTER TABLE "candidates" ALTER COLUMN "skills" SET DEFAULT '{}'`);
        await queryRunner.query(`CREATE INDEX "idx_candidates_job_status_tier" ON "candidates" ("jobId", "status", "tier") `);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "updatedAt" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "createdAt" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "role" SET DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "company" SET DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "phoneNumber" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "waitlist" ALTER COLUMN "fullName" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "waitlist" DROP CONSTRAINT "UQ_2221cffeeb64bff14201bd5b3de"`);
        await queryRunner.query(`CREATE TYPE "public"."videojd_virtualbackgroundtype_enum_old" AS ENUM('NONE', 'BLUR', 'IMAGE')`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "virtualBackgroundType" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "virtualBackgroundType" TYPE "public"."videojd_virtualbackgroundtype_enum_old" USING "virtualBackgroundType"::"text"::"public"."videojd_virtualbackgroundtype_enum_old"`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "virtualBackgroundType" SET DEFAULT 'NONE'`);
        await queryRunner.query(`DROP TYPE "public"."videoJDs_virtualbackgroundtype_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."videojd_virtualbackgroundtype_enum_old" RENAME TO "videojd_virtualbackgroundtype_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."videojd_type_enum_old" AS ENUM('AI_AVATAR', 'LIVE_RECORDING')`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "type" TYPE "public"."videojd_type_enum_old" USING "type"::"text"::"public"."videojd_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "videoJDs" ALTER COLUMN "type" SET DEFAULT 'AI_AVATAR'`);
        await queryRunner.query(`DROP TYPE "public"."videoJDs_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."videojd_type_enum_old" RENAME TO "videojd_type_enum"`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "autoJoinEnabled" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "subscriptionPaymentId"`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "subscriptionPaymentId" character varying(255)`);
        await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "subscriptionPaymentMethod"`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "subscriptionPaymentMethod" character varying(255)`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionAutoRenew" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionCredits" SET DEFAULT '{"usedCredits": 0, "totalCredits": 250, "lastResetDate": "2024-01-01T00:00:00.000Z", "atsIntegration": "basic", "activeJobsLimit": 1, "teamMembersUsed": 0, "remainingCredits": 250, "teamMembersLimit": 1, "monthlyAllocation": 250, "videoJdMaxDuration": 60, "databaseRetentionMonths": 1}'`);
        await queryRunner.query(`CREATE TYPE "public"."subscription_plan_enum_old" AS ENUM('starter', 'professional', 'enterprise', 'free')`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionPlan" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionPlan" TYPE "public"."subscription_plan_enum_old" USING "subscriptionPlan"::"text"::"public"."subscription_plan_enum_old"`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "subscriptionPlan" SET DEFAULT 'starter'`);
        await queryRunner.query(`DROP TYPE "public"."companies_subscriptionplan_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."subscription_plan_enum_old" RENAME TO "subscription_plan_enum"`);
        await queryRunner.query(`ALTER TABLE "companies" ALTER COLUMN "isApproved" DROP DEFAULT`);
        await queryRunner.query(`CREATE TYPE "public"."invitation_status_enum_old" AS ENUM('pending', 'accepted', 'expired', 'cancelled')`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "status" TYPE "public"."invitation_status_enum_old" USING "status"::"text"::"public"."invitation_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."company_invitations_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."invitation_status_enum_old" RENAME TO "invitation_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."company_member_role_enum_old" AS ENUM('owner', 'admin', 'member', 'viewer')`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "role" TYPE "public"."company_member_role_enum_old" USING "role"::"text"::"public"."company_member_role_enum_old"`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ALTER COLUMN "role" SET DEFAULT 'member'`);
        await queryRunner.query(`DROP TYPE "public"."company_invitations_role_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."company_member_role_enum_old" RENAME TO "company_member_role_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."company_member_status_enum_old" AS ENUM('active', 'invited', 'suspended')`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "status" TYPE "public"."company_member_status_enum_old" USING "status"::"text"::"public"."company_member_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "status" SET DEFAULT 'invited'`);
        await queryRunner.query(`DROP TYPE "public"."company_members_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."company_member_status_enum_old" RENAME TO "company_member_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."company_member_role_enum_old" AS ENUM('owner', 'admin', 'member', 'viewer')`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "role" TYPE "public"."company_member_role_enum_old" USING "role"::"text"::"public"."company_member_role_enum_old"`);
        await queryRunner.query(`ALTER TABLE "company_members" ALTER COLUMN "role" SET DEFAULT 'member'`);
        await queryRunner.query(`DROP TYPE "public"."company_members_role_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."company_member_role_enum_old" RENAME TO "company_member_role_enum"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`CREATE INDEX "idx_recruitment_assessments_created_at" ON "recruitment_assessments" ("created_at") `);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ALTER COLUMN "is_completed" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "maturity_level"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "maturity_level" character varying(50)`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "company"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "company" character varying(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "position"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "position" character varying(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "phone_number"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "phone_number" character varying(20)`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "email"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "email" character varying(255) NOT NULL`);
        await queryRunner.query(`CREATE INDEX "idx_recruitment_assessments_email" ON "recruitment_assessments" ("email") `);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "last_name"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "last_name" character varying(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" DROP COLUMN "first_name"`);
        await queryRunner.query(`ALTER TABLE "recruitment_assessments" ADD "first_name" character varying(255) NOT NULL`);
        await queryRunner.query(`CREATE TYPE "public"."candidate_status_enum_old" AS ENUM('NEW', 'MATCHED', 'CONTACTED', 'INTERESTED', 'NOT_INTERESTED', 'INTERVIEWING', 'OFFER_PENDING_APPROVAL', 'OFFER_APPROVED', 'OFFER_REJECTED', 'OFFER_EXTENDED', 'OFFER_ACCEPTED', 'OFFER_DECLINED', 'HIRE_PENDING_APPROVAL', 'HIRE_APPROVED', 'HIRED', 'REJECTED', 'WITHDRAWN', 'CULTURAL_FIT_ANSWERED', 'SHORTLISTED', 'MATCH_MODIFIED')`);
        await queryRunner.query(`ALTER TABLE "candidate_evaluations" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidate_evaluations" ALTER COLUMN "status" TYPE "public"."candidate_status_enum_old" USING "status"::"text"::"public"."candidate_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "candidate_evaluations" ALTER COLUMN "status" SET DEFAULT 'NEW'`);
        await queryRunner.query(`DROP TYPE "public"."candidate_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."candidate_status_enum_old" RENAME TO "candidate_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."candidate_status_enum_old" AS ENUM('NEW', 'MATCHED', 'CONTACTED', 'INTERESTED', 'NOT_INTERESTED', 'INTERVIEWING', 'OFFER_PENDING_APPROVAL', 'OFFER_APPROVED', 'OFFER_REJECTED', 'OFFER_EXTENDED', 'OFFER_ACCEPTED', 'OFFER_DECLINED', 'HIRE_PENDING_APPROVAL', 'HIRE_APPROVED', 'HIRED', 'REJECTED', 'WITHDRAWN', 'CULTURAL_FIT_ANSWERED', 'SHORTLISTED', 'MATCH_MODIFIED')`);
        await queryRunner.query(`ALTER TABLE "candidate_applications" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "candidate_applications" ALTER COLUMN "status" TYPE "public"."candidate_status_enum_old" USING "status"::"text"::"public"."candidate_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "candidate_applications" ALTER COLUMN "status" SET DEFAULT 'NEW'`);
        await queryRunner.query(`DROP TYPE "public"."candidate_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."candidate_status_enum_old" RENAME TO "candidate_status_enum"`);
        await queryRunner.query(`ALTER TABLE "candidate_profiles" ALTER COLUMN "extractionMetadata" SET DEFAULT '[]'`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ALTER COLUMN "onboardingProgress" SET DEFAULT '{"overall": {"completed": false, "percentage": 0, "completedAt": null}, "basicInfo": {"completed": false, "percentage": 0, "completedAt": null, "requiredFields": ["firstName", "lastName", "email"], "completedFields": []}, "lastUpdated": null, "preferences": {"completed": false, "percentage": 0, "completedAt": null, "requiredFields": ["jobTypes"], "completedFields": []}, "additionalInfo": {"completed": false, "percentage": 0, "completedAt": null, "requiredFields": [], "completedFields": []}, "professionalInfo": {"completed": false, "percentage": 0, "completedAt": null, "requiredFields": ["skills"], "completedFields": []}}'`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ALTER COLUMN "myValues" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ALTER COLUMN "languages" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ALTER COLUMN "skills" SET DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "job_seekers" ADD CONSTRAINT "UQ_2fdf8401dc0dd4fbc082447aa23" UNIQUE ("clientId")`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP COLUMN "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT now()`);
        await queryRunner.query(`CREATE INDEX "idx_credit_purchases_created_at" ON "credit_purchases" ("createdAt") `);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."metadata" IS 'Additional metadata about the purchase'`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "metadata" DROP NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."status" IS 'Current status of the purchase (PENDING, COMPLETED, FAILED, REFUNDED)'`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "status" DROP NOT NULL`);
        await queryRunner.query(`CREATE TYPE "public"."credit_purchase_status_enum_old" AS ENUM('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED')`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "status" TYPE "public"."credit_purchase_status_enum_old" USING "status"::"text"::"public"."credit_purchase_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "status" SET DEFAULT 'PENDING'`);
        await queryRunner.query(`DROP TYPE "public"."credit_purchases_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."credit_purchase_status_enum_old" RENAME TO "credit_purchase_status_enum"`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."stripeSessionId" IS 'Stripe checkout session ID for verification'`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."paymentId" IS 'External payment system transaction ID'`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."paymentMethod" IS 'Payment method used (stripe, paypal, etc.)'`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "paymentMethod" DROP NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."totalCredits" IS 'Total credits (base + bonus) added to account'`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."bonusCredits" IS 'Bonus credits added to the package'`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."baseCredits" IS 'Base number of credits in the package'`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."amountPaid" IS 'Amount paid in USD for the credit package'`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."packageType" IS 'Type of credit package purchased (SMALL, MEDIUM, LARGE, etc.)'`);
        await queryRunner.query(`CREATE TYPE "public"."credit_package_enum_old" AS ENUM('SMALL', 'MEDIUM', 'LARGE', 'EXTRA_LARGE', 'MEGA')`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ALTER COLUMN "packageType" TYPE "public"."credit_package_enum_old" USING "packageType"::"text"::"public"."credit_package_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."credit_purchases_packagetype_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."credit_package_enum_old" RENAME TO "credit_package_enum"`);
        await queryRunner.query(`COMMENT ON COLUMN "credit_purchases"."companyId" IS 'Reference to the company that made the purchase'`);
        await queryRunner.query(`CREATE TYPE "public"."feedback_category_enum" AS ENUM('FEATURE', 'BUG', 'IMPROVEMENT', 'OTHER')`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD "category" "public"."feedback_category_enum" NOT NULL DEFAULT 'FEATURE'`);
        await queryRunner.query(`ALTER TABLE "companies" ADD "subscriptionLimits" jsonb DEFAULT jsonb_build_object('resumeUploadLimit', 100, 'resumeUploadUsed', 0, 'scoutLimit', 20, 'scoutUsed', 0, 'matchRankLimit', 100, 'matchRankUsed', 0, 'videoJdLimit', 2, 'videoJdUsed', 0, 'videoJdMaxDuration', 90, 'cultureFitQuestionsLimit', 5, 'jobDescriptionLimit', 5, 'jobDescriptionUsed', 0, 'activeJobsLimit', 3, 'teamMembersLimit', 2, 'atsIntegration', 'basic', 'databaseRetentionMonths', 3, 'lastResetDate', to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'))`);
        await queryRunner.query(`DROP TABLE "email_history"`);
        await queryRunner.query(`DROP TYPE "public"."email_history_status_enum"`);
        await queryRunner.query(`COMMENT ON TABLE "feature_flags" IS 'Feature flags for controlling application features and A/B testing'`);
        await queryRunner.query(`COMMENT ON TABLE "contacts" IS 'Table for storing contact form submissions and inquiries'`);
        await queryRunner.query(`COMMENT ON TABLE "credit_purchases" IS 'Tracks one-time credit purchases made by companies'`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD CONSTRAINT "candidate_comparisons_status_check" CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'completed'::character varying, 'failed'::character varying])::text[])))`);
        await queryRunner.query(`ALTER TABLE "career_insights" ADD CONSTRAINT "check_ai_insights" CHECK ((("aiInsights" IS NULL) OR (("aiInsights" ? 'strengths'::text) AND ("aiInsights" ? 'opportunities'::text) AND ("aiInsights" ? 'challenges'::text) AND ("aiInsights" ? 'recommendations'::text) AND ("aiInsights" ? 'confidenceScore'::text))))`);
        await queryRunner.query(`ALTER TABLE "career_insights" ADD CONSTRAINT "check_skill_gap_analysis" CHECK ((("skillGapAnalysis" IS NULL) OR (("skillGapAnalysis" ? 'currentSkills'::text) AND ("skillGapAnalysis" ? 'targetRole'::text) AND ("skillGapAnalysis" ? 'skillGaps'::text) AND ("skillGapAnalysis" ? 'estimatedTimeToClose'::text) AND ("skillGapAnalysis" ? 'overallReadinessScore'::text))))`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD CONSTRAINT "chk_total_credits_calculation" CHECK (("totalCredits" = ("baseCredits" + "bonusCredits")))`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD CONSTRAINT "chk_credits_positive" CHECK ((("baseCredits" > 0) AND ("bonusCredits" >= 0) AND ("totalCredits" > 0)))`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD CONSTRAINT "chk_amount_paid_positive" CHECK (("amountPaid" > (0)::numeric))`);
        await queryRunner.query(`CREATE INDEX "IDX_CREDIT_USAGE_COMPANY_ACTION" ON "credit_usage_history" ("actionType", "companyId") `);
        await queryRunner.query(`CREATE INDEX "IDX_CREDIT_USAGE_COMPANY_CREATED" ON "credit_usage_history" ("companyId", "createdAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_category" ON "feedback" ("category") `);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_status" ON "feedback" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_companyId" ON "feedback" ("companyId") `);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_clientId" ON "feedback" ("clientId") `);
        await queryRunner.query(`CREATE INDEX "IDX_FEATURE_FLAG_EXPIRES_AT" ON "feature_flags" ("expiresAt") WHERE ("expiresAt" IS NOT NULL)`);
        await queryRunner.query(`CREATE INDEX "IDX_FEATURE_FLAG_IS_ACTIVE" ON "feature_flags" ("isActive") `);
        await queryRunner.query(`CREATE INDEX "IDX_FEATURE_FLAG_UPDATED_BY" ON "feature_flags" ("updatedBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_FEATURE_FLAG_CREATED_BY" ON "feature_flags" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "idx_contacts_created_at" ON "contacts" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_SCOUTED_CANDIDATES_COMPANY_JOB_SEARCH" ON "scouted_candidates" ("companyContext", "isAdded", "jobId", "searchSource") `);
        await queryRunner.query(`CREATE INDEX "IDX_SCOUTED_CANDIDATES_APOLLO_METADATA" ON "scouted_candidates" ("apolloMetadata") `);
        await queryRunner.query(`CREATE INDEX "IDX_SCOUTED_CANDIDATES_COMPANY_CONTEXT" ON "scouted_candidates" ("companyContext") `);
        await queryRunner.query(`CREATE INDEX "IDX_SCOUTED_CANDIDATES_SEARCH_SOURCE" ON "scouted_candidates" ("searchSource") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_with_notes" ON "candidates" ("clientId") WHERE ("scrapebookNotes" <> '{}'::jsonb)`);
        await queryRunner.query(`CREATE INDEX "idx_candidates_management_status" ON "candidates" ("clientId", "isFavorited", "isShortlisted") WHERE (("isFavorited" = true) OR ("isShortlisted" = true))`);
        await queryRunner.query(`CREATE INDEX "idx_candidates_scrapebook_notes" ON "candidates" ("scrapebookNotes") `);
        await queryRunner.query(`CREATE INDEX "idx_candidates_is_shortlisted" ON "candidates" ("isShortlisted") WHERE ("isShortlisted" = true)`);
        await queryRunner.query(`CREATE INDEX "idx_candidates_is_favorited" ON "candidates" ("isFavorited") WHERE ("isFavorited" = true)`);
        await queryRunner.query(`CREATE INDEX "idx_candidates_email_correspondence_gin" ON "candidates" ("emailCorrespondence") `);
        await queryRunner.query(`CREATE INDEX "IDX_WAITLIST_STRIPE_SESSION" ON "waitlist" ("stripeSessionId") `);
        await queryRunner.query(`CREATE INDEX "IDX_WAITLIST_STATUS" ON "waitlist" ("status") `);
        await queryRunner.query(`CREATE INDEX "idx_job_seekers_activity_history_gin" ON "job_seekers" ("activityHistory") `);
        await queryRunner.query(`CREATE INDEX "idx_job_seekers_email_correspondence_gin" ON "job_seekers" ("emailCorrespondence") `);
        await queryRunner.query(`CREATE INDEX "idx_credit_purchases_stripe_session_id" ON "credit_purchases" ("stripeSessionId") `);
        await queryRunner.query(`CREATE INDEX "idx_credit_purchases_payment_id" ON "credit_purchases" ("paymentId") `);
        await queryRunner.query(`ALTER TABLE "credit_usage_history" ADD CONSTRAINT "fk_credit_usage_history_company" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD CONSTRAINT "FK_feedback_company" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "candidate_comparisons" ADD CONSTRAINT "fk_comparison_job" FOREIGN KEY ("jobId") REFERENCES "jobs"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "career_insights" ADD CONSTRAINT "fk_career_insights_job_seeker" FOREIGN KEY ("jobSeekerId") REFERENCES "job_seekers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "scouted_candidates" ADD CONSTRAINT "FK_scouted_candidates_job" FOREIGN KEY ("jobId") REFERENCES "jobs"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "company_invitations" ADD CONSTRAINT "FK_company_invitations_company" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "company_members" ADD CONSTRAINT "FK_company_members_company" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "credit_purchases" ADD CONSTRAINT "fk_credit_purchases_company" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
