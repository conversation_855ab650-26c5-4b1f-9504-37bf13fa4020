# Candidate Comparison Feature - Usage Guide

## Overview
This feature allows users to select 2-3 candidates from a list and compare them using various comparison types, including AI-powered analysis. The comparison results provide detailed insights to help make informed hiring decisions.

## Key Components

### 1. **CandidateListWithSelection**
The main component that wraps the candidate list and provides selection functionality.

### 2. **SelectionActionsSlider**
Bottom slider that appears when candidates are selected, providing actions like Compare, Send Video Intro, Delete, etc.

### 3. **ComparisonOptionsSlider**
Modal that shows comparison type options (Quick Overview, Skills Deep Dive, etc.) or custom comparison.

### 4. **ComparisonResults**
Component that displays the comparison results after processing.

## Integration Example

### Replace existing candidate list with the selection-enabled version:

```tsx
// In your job candidates page (e.g., /app/jobs/[id]/candidates/page.tsx)
import { CandidateListWithSelection } from '@/components/MatchRank';

export default function CandidatesPage({ params }: { params: { id: string } }) {
  const jobId = params.id;
  const [candidates, setCandidates] = useState<ICandidate[]>([]);

  // Your existing candidate fetching logic...

  return (
    <div>
      {/* Replace your existing candidate list with: */}
      <CandidateListWithSelection
        candidates={candidates}
        jobId={jobId}
        onCandidateSelect={(candidate) => {
          // Handle individual candidate selection if needed
        }}
        onStatusUpdate={() => {
          // Refresh candidates if needed
        }}
        topCandidateThreshold={80}
        secondTierCandidateThreshold={60}
      />
    </div>
  );
}
```

### Add comparison results page:

```tsx
// Create: /app/jobs/[id]/comparisons/[comparisonId]/page.tsx
import { ComparisonResults } from '@/components/MatchRank';

export default function ComparisonResultsPage({ 
  params 
}: { 
  params: { id: string; comparisonId: string } 
}) {
  return (
    <ComparisonResults
      jobId={params.id}
      comparisonId={params.comparisonId}
    />
  );
}
```

## Features

### Selection Mode
- Click "Select to Compare" button to enter selection mode
- Checkboxes appear next to each candidate's avatar
- Maximum 3 candidates can be selected
- Visual feedback shows selected count

### Available Actions
When candidates are selected, the bottom slider shows:

**Left Actions:**
- **Compare Candidates** - Opens comparison options (requires 2+ selections)
- **Send Video Intro Email** - Send video introductions to selected candidates

**Right Actions:**
- **Export** - Export candidate data
- **Share** - Share selected candidates
- **Delete** - Remove selected candidates

### Comparison Types
1. **Quick Overview** (30 seconds) - Rapid comparison of key metrics
2. **Skills & Technical Fit** (2 minutes) - Deep dive into technical capabilities
3. **Leadership & Management** (2 minutes) - Leadership experience comparison
4. **Cultural & Team Fit** (1.5 minutes) - Team integration assessment
5. **Cost-Benefit Analysis** (2 minutes) - Compensation vs. value analysis
6. **Risk Assessment** (1.5 minutes) - Potential risks comparison
7. **Custom Comparison** - Write your own comparison prompt

### Comparison Flow
1. Select 2-3 candidates
2. Click "Compare Candidates"
3. Choose comparison type or write custom prompt
4. System processes comparison (shows loading state)
5. Redirects to comparison results page
6. Results show:
   - Executive summary
   - Top recommendation with reasoning
   - Individual candidate analysis
   - Alternative scenarios
   - Critical considerations

## API Integration

The feature integrates with the backend comparison API:
- `POST /api/comparisons` - Create new comparison
- `GET /api/comparisons/:id` - Get comparison results
- `GET /api/comparisons/:id/status` - Check processing status

## Customization

### Styling
All components use Tailwind CSS and follow the existing design system. Colors use the purple/pink gradient theme.

### Behavior
- Selection is limited to 3 candidates maximum
- Comparison requires minimum 2 candidates
- All actions show appropriate toast notifications
- Loading states are handled gracefully

## Future Enhancements
The following features are marked as "coming soon" and can be implemented:
- Video intro email functionality
- Bulk delete operations
- Export to various formats (PDF, CSV)
- Share via email or link
- Comparison history
- Save comparison templates