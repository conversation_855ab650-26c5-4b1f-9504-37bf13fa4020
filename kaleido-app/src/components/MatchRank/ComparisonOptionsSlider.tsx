import { AnimatePresence, motion } from 'framer-motion';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Edit2, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Handshake,
  DollarSign,
  AlertTriangle
} from 'lucide-react';
import React, { useState } from 'react';
import { COMPARISON_OPTIONS, ComparisonType } from '@/types/comparison.types';
import { ICandidate } from '@/entities/interfaces';

interface ComparisonOptionsSliderProps {
  isVisible: boolean;
  selectedCandidates: ICandidate[];
  onClose: () => void;
  onSelectOption: (type: ComparisonType, customPrompt?: string) => void;
  className?: string;
}

export const ComparisonOptionsSlider: React.FC<ComparisonOptionsSliderProps> = ({
  isVisible,
  selectedCandidates,
  onClose,
  onSelectOption,
  className = '',
}) => {
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedOption, setSelectedOption] = useState<ComparisonType | null>(null);

  const handleOptionClick = (type: ComparisonType) => {
    if (type === ComparisonType.CUSTOM) {
      setShowCustomPrompt(true);
      setSelectedOption(type);
    } else {
      onSelectOption(type);
    }
  };

  const handleCustomSubmit = () => {
    if (customPrompt.trim()) {
      onSelectOption(ComparisonType.CUSTOM, customPrompt);
      setCustomPrompt('');
      setShowCustomPrompt(false);
    }
  };

  const candidateNames = selectedCandidates.map(c => c.fullName).join(', ');

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: '100%', opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: '100%', opacity: 0 }}
          transition={{
            type: 'spring',
            damping: 25,
            stiffness: 300,
          }}
          className={`fixed bottom-0 left-0 right-0 z-[60] ${className}`}
        >
          {/* Backdrop for bottom sheet */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Bottom sheet container */}
          <motion.div
            className="relative bg-gradient-to-b from-slate-900 to-purple-950/50 backdrop-blur-xl rounded-t-3xl shadow-2xl shadow-purple-500/40 border-t border-purple-400/40 border-x border-purple-400/20"
            initial={{ borderRadius: '0 0 0 0' }}
            animate={{ borderRadius: '24px 24px 0 0' }}
          >
            {/* Refined gradient shadows with slider color flow */}
            <div className="absolute inset-x-0 -top-20 h-20 bg-gradient-to-t from-slate-900/70 via-purple-950/50 to-transparent blur-2xl" />
            <div className="absolute inset-x-0 -top-16 h-16 bg-gradient-to-t from-purple-950/60 via-purple-800/35 to-transparent blur-xl" />
            <div className="absolute inset-x-0 -top-12 h-12 bg-gradient-to-t from-purple-900/50 via-purple-600/25 to-transparent blur-lg" />
            <div className="absolute inset-x-0 -top-8 h-8 bg-gradient-to-t from-purple-950/40 via-purple-500/15 to-transparent blur-md" />
            {/* Top border glow effect */}
            <div className="absolute top-0 left-0 right-0 h-[3px] bg-gradient-to-r from-transparent via-purple-400/50 to-transparent rounded-t-3xl" />
            <div className="absolute -top-1 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-pink-400/30 to-transparent rounded-t-3xl" />
            
            {/* Enhanced handle bar */}
            <div className="flex justify-center pt-4">
              <div className="w-12 h-1.5 bg-gradient-to-r from-purple-400/40 via-white/30 to-purple-400/40 rounded-full shadow-lg" />
            </div>

            {/* Header */}
            <div className="px-4 pt-4 pb-5">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold text-white">Choose Comparison Type</h2>
                  <p className="text-white/60 text-xs mt-0.5">
                    Comparing {selectedCandidates.length} candidates
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-1.5 hover:bg-white/10 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-white/70" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="px-4 pb-8 max-h-[65vh] overflow-y-auto">
              {!showCustomPrompt ? (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {COMPARISON_OPTIONS.map((option) => (
                    <motion.button
                      key={option.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleOptionClick(option.id as ComparisonType)}
                      className="group relative p-2 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-purple-400/30 rounded-md transition-all duration-200 text-left overflow-hidden"
                    >
                      {/* Background gradient on hover */}
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity" />

                      <div className="relative z-10">
                        <div className="flex items-center justify-center mb-1.5">
                          {option.id === ComparisonType.QUICK_OVERVIEW && <Zap className="w-4 h-4 text-purple-400" />}
                          {option.id === ComparisonType.SKILLS_DEEP_DIVE && <Wrench className="w-4 h-4 text-blue-400" />}
                          {option.id === ComparisonType.LEADERSHIP_COMPARISON && <Users className="w-4 h-4 text-orange-400" />}
                          {option.id === ComparisonType.CULTURAL_TEAM_FIT && <Handshake className="w-4 h-4 text-green-400" />}
                          {option.id === ComparisonType.COST_BENEFIT && <DollarSign className="w-4 h-4 text-yellow-400" />}
                          {option.id === ComparisonType.RISK_ASSESSMENT && <AlertTriangle className="w-4 h-4 text-red-400" />}
                        </div>
                        
                        <h3 className="text-xs font-semibold text-white text-center mb-1">
                          {option.name}
                        </h3>
                        
                        <p className="text-xs text-white/50 text-center leading-tight mb-1">
                          {option.description}
                        </p>

                        <div className="flex items-center justify-center gap-1 text-xs text-white/40">
                          <Clock className="w-2.5 h-2.5" />
                          <span>{option.estimatedTime}</span>
                        </div>
                      </div>
                    </motion.button>
                  ))}

                  {/* Custom comparison option */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleOptionClick(ComparisonType.CUSTOM)}
                    className="group relative p-2 bg-gradient-to-br from-purple-500/10 to-pink-500/10 hover:from-purple-500/20 hover:to-pink-500/20 border border-purple-400/30 hover:border-purple-400/50 rounded-md transition-all duration-200 text-left overflow-hidden"
                  >
                    <div className="relative z-10">
                      <div className="flex items-center justify-center mb-1.5">
                        <Sparkles className="w-4 h-4 text-purple-400" />
                      </div>
                      
                      <h3 className="text-xs font-semibold text-white text-center mb-1">
                        Custom
                      </h3>
                      
                      <p className="text-xs text-white/50 text-center leading-tight mb-1">
                        Create your own comparison criteria
                      </p>

                      <div className="flex items-center justify-center text-purple-400 text-xs font-medium">
                        <Edit2 className="w-2.5 h-2.5 mr-1" />
                        <span>Custom</span>
                      </div>
                    </div>
                  </motion.button>
                </div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-3"
                >
                  <div>
                    <label className="block text-xs font-medium text-white/80 mb-1.5">
                      What would you like to compare?
                    </label>
                    <textarea
                      value={customPrompt}
                      onChange={(e) => setCustomPrompt(e.target.value)}
                      placeholder="E.g., Compare their experience with remote team management, or their potential to grow into a senior leadership role..."
                      className="w-full h-20 px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-xs text-white placeholder-white/40 focus:outline-none focus:border-purple-400/50 focus:ring-2 focus:ring-purple-400/20 resize-none"
                      autoFocus
                    />
                  </div>

                  <div className="text-xs text-white/40">
                    <p className="font-medium mb-0.5">💡 Tips: Be specific, consider team needs, ask about scenarios</p>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <button
                      onClick={() => {
                        setShowCustomPrompt(false);
                        setCustomPrompt('');
                      }}
                      className="flex-1 px-3 py-1.5 bg-white/10 hover:bg-white/15 border border-white/20 text-white text-sm rounded-lg transition-colors"
                    >
                      Back
                    </button>
                    <button
                      onClick={handleCustomSubmit}
                      disabled={!customPrompt.trim()}
                      className="flex-1 px-3 py-1.5 bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white text-sm rounded-lg transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Start Comparison
                    </button>
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ComparisonOptionsSlider;