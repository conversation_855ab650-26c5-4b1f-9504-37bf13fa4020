import { create } from 'zustand';
import { ICandidate } from '@/entities/interfaces';

interface CandidateSelectionState {
  selectedCandidates: ICandidate[];
  maxSelections: number;
  
  // Actions
  toggleCandidate: (candidate: ICandidate) => void;
  clearSelection: () => void;
  
  // Computed values
  selectedCount: number;
  canAddMore: boolean;
  isSelected: (candidateId: string) => boolean;
}

export const useCandidateSelectionStore = create<CandidateSelectionState>((set, get) => ({
  selectedCandidates: [],
  maxSelections: 3,
  selectedCount: 0,
  canAddMore: true,

  toggleCandidate: (candidate: ICandidate) => {
    const state = get();
    const isCurrentlySelected = state.selectedCandidates.some(c => c.id === candidate.id);
    
    if (isCurrentlySelected) {
      // Remove from selection
      set({
        selectedCandidates: state.selectedCandidates.filter(c => c.id !== candidate.id),
        selectedCount: state.selectedCandidates.length - 1,
        canAddMore: true,
      });
    } else if (state.selectedCandidates.length < state.maxSelections) {
      // Add to selection
      const newSelection = [...state.selectedCandidates, candidate];
      set({
        selectedCandidates: newSelection,
        selectedCount: newSelection.length,
        canAddMore: newSelection.length < state.maxSelections,
      });
    }
  },

  clearSelection: () => {
    set({ 
      selectedCandidates: [],
      selectedCount: 0,
      canAddMore: true,
    });
  },

  isSelected: (candidateId: string) => {
    return get().selectedCandidates.some(c => c.id === candidateId);
  },
}));